name: Precium App Deploy
on:
  push:
    branches:
      - dev

jobs:
  firebase-build:
    name: Firebase Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout <PERSON><PERSON>
        uses: actions/checkout@master
      - name: Install Dependencies
        run: npm install --legacy-peer-deps
      - name: Build
        run: npm run deploy-dev
        env:
          CI: ""
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

  # docker-build:
  #   name: Docker build and push
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Check out source code
  #       uses: actions/checkout@master
  #     - name: Docker login
  #       uses: azure/docker-login@v1
  #       with:
  #         username: ${{ secrets.DOCKER_USERNAME }}
  #         password: ${{ secrets.DOCKER_PASSWORD }}
  #     - name: Docker build & tag
  #       run: docker build  --build-arg ENV=development . --tag ${{ secrets.IMAGE_NAME }}:dev-latest --tag ${{ secrets.IMAGE_NAME }}:${GITHUB_SHA::7}
  #     - name: Docker dev push
  #       run: |
  #         docker push ${{ secrets.IMAGE_NAME }}:dev-latest
  #         docker push ${{ secrets.IMAGE_NAME }}:${GITHUB_SHA::7}

  # deploy:
  #   needs: docker-build
  #   name: Deploy on K8s
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Docker login
  #       uses: azure/docker-login@v1
  #       with:
  #         username: ${{ secrets.DOCKER_USERNAME }}
  #         password: ${{ secrets.DOCKER_PASSWORD }}
  #     - name: Install K8s
  #       uses: GoogleCloudPlatform/github-actions/setup-gcloud@master
  #       with:
  #         version: "290.0.1"
  #         project_id: precium-293808
  #         service_account_key: ${{ secrets.GKE_SERVICE_JSON }}
  #         export_default_credentials: true
  #     - name: Setup K8s
  #       run: |
  #         gcloud container clusters get-credentials cluster-1 --zone asia-southeast1-a --project precium-293808
  #         kubectl set image deployment -n precium-app-dev precium-app precium-app=${{ secrets.IMAGE_NAME }}:${GITHUB_SHA::7} --record

  # slack-workflow-status:
  #   if: always()
  #   name: Post Workflow Status To Slack
  #   needs:
  #     - firebase-build
  #     - docker-build
  #     - deploy
  #   runs-on: ubuntu-latest
  #   steps:
  #     - name: Slack Workflow Notification
  #       uses: Gamesight/slack-workflow-status@master
  #       with:
  #         # Required Input
  #         repo_token: ${{secrets.GITHUB_TOKEN}}
  #         slack_webhook_url: ${{secrets.SLACK_WEBHOOK_URL}}
  #         # Optional Input
  #         channel: "#precium-build"
  #         name: "WEB DEV"
