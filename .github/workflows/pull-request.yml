name: Deploy to Firebase Hosting on PR
'on': pull_request
jobs:
  build_and_preview:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - run: 'export CI=false && npm install --legacy-peer-deps && REACT_APP_DEP_TYPE=development npm run build'
      # Add additional build steps here
      # - run: ...
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: '${{ secrets.GITHUB_TOKEN }}'
          firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_PRECIUM_UI }}'
          projectId: precium-ui
          # default expire value 7 days
          # expires: 7d
        env:
          FIREBASE_CLI_PREVIEWS: hostingchannels
