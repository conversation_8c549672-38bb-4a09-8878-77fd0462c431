<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/logo-purple.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Hotel Price Recommendation System" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo-purple.svg" />
    <!-- <link
      href="https://fonts.googleapis.com/css2?family=Roboto&family=Roboto&family=Mulish:ital,wght@1,300&display=swap"
      rel="stylesheet"
    /> -->
    <link
      href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:wght@500&display=swap"
      rel="stylesheet"
    />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <style>
      body {
        width: 100%;
        height: 100%;
        background-color: #f5f4f8;
      }
      #root {
        box-sizing: border-box;
        width: 100%;
        height: 100vh;
      }
    </style>
    <title>Precium</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
