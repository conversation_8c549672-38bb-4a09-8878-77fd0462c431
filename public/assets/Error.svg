<svg id="Group_170" data-name="Group 170" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="361.559" height="277.81" viewBox="0 0 361.559 277.81">
  <defs>
    <style>
      .cls-1, .cls-10, .cls-4 {
        fill: #6c63ff;
      }

      .cls-1, .cls-2, .cls-5, .cls-9 {
        opacity: 0.1;
      }

      .cls-3 {
        fill: #3f3d56;
      }

      .cls-6 {
        fill: #d0d2d5;
      }

      .cls-7 {
        fill: #fff;
      }

      .cls-8 {
        fill: #322277;
      }

      .cls-9 {
        fill: #444053;
      }

      .cls-10 {
        opacity: 0.3;
      }

      .cls-11 {
        fill: #fa5959;
      }

      .cls-11, .cls-12, .cls-13 {
        opacity: 0.8;
      }

      .cls-12 {
        fill: #fed253;
      }

      .cls-13 {
        fill: #8ccf4d;
      }

      .cls-14 {
        fill: url(#linear-gradient);
      }

      .cls-15 {
        fill: #2f2e41;
      }

      .cls-16 {
        fill: #454b69;
      }

      .cls-17 {
        fill: #fbbebe;
      }
    </style>
    <linearGradient id="linear-gradient" x1="0.459" y1="0.999" x2="0.459" y2="-0.006" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="gray" stop-opacity="0.251"/>
      <stop offset="0.54" stop-color="gray" stop-opacity="0.122"/>
      <stop offset="1" stop-color="gray" stop-opacity="0.102"/>
    </linearGradient>
  </defs>
  <ellipse id="Ellipse_43" data-name="Ellipse 43" class="cls-1" cx="150.252" cy="11.667" rx="150.252" ry="11.667" transform="translate(15.425 221.976)"/>
  <g id="Group_165" data-name="Group 165" class="cls-2" transform="translate(240.347 32.536)">
    <path id="Path_317" data-name="Path 317" class="cls-3" d="M863.074,470.959c-.9-2.726-4.539-3.981-8.131-2.8a8.754,8.754,0,0,0-1.5.647c-.23-.064-.467-.12-.707-.163a7.928,7.928,0,0,0,.223-2.4,8.7,8.7,0,0,0,3.854-7.59,8.708,8.708,0,0,0,3.839-7.59,8.4,8.4,0,0,0,2.91-3.295c1.732-3.429,1.015-7.283-1.605-8.609s-6.151.382-7.884,3.811a8.375,8.375,0,0,0-.93,4.3,8.708,8.708,0,0,0-3.839,7.59,8.708,8.708,0,0,0-3.839,7.59,8.711,8.711,0,0,0-3.854,7.594,8.4,8.4,0,0,0-2.91,3.295,9,9,0,0,0-.735,2.029,7.488,7.488,0,0,0-1.57-1.672,8.974,8.974,0,0,0-.354-1.591c-1.181-3.6-4.348-5.787-7.071-4.889s-3.981,4.536-2.8,8.131a8.156,8.156,0,0,0,2.673,3.917,8.98,8.98,0,0,0,.354,1.591,7.779,7.779,0,0,0,3.588,4.508,8.217,8.217,0,0,0-.293,1.379,8.941,8.941,0,0,0-2.666,5.271,8.934,8.934,0,0,0-2.666,5.275,8.709,8.709,0,0,0-1.768,2.408c-1.736,3.429-1.018,7.286,1.6,8.609s6.151-.378,7.887-3.811a8.7,8.7,0,0,0,.884-2.86,8.941,8.941,0,0,0,2.666-5.271,8.934,8.934,0,0,0,2.666-5.275,8.913,8.913,0,0,0,2.634-5.271,8.913,8.913,0,0,0,2.673-5.282,8.524,8.524,0,0,0,1.2-1.414,8.294,8.294,0,0,0,4.338-.272,8.752,8.752,0,0,0,1.5-.647,8.2,8.2,0,0,0,4.744-.17C861.766,476.856,863.969,473.688,863.074,470.959Z" transform="translate(-804.668 -336.33)"/>
    <g id="Group_163" data-name="Group 163" class="cls-2" transform="translate(20.161 111.907)">
      <path id="Path_318" data-name="Path 318" d="M912.829,499.53a7.6,7.6,0,0,1-1.216.972,8.13,8.13,0,0,1-.392,2.98,9.251,9.251,0,0,0,.742-1.213,8.711,8.711,0,0,0,.866-2.74Z" transform="translate(-880.922 -487.556)"/>
      <path id="Path_319" data-name="Path 319" d="M923.689,478.06a7.607,7.607,0,0,1-1.216.972,8.131,8.131,0,0,1-.392,2.98,9.07,9.07,0,0,0,.742-1.216A8.787,8.787,0,0,0,923.689,478.06Z" transform="translate(-887.943 -473.676)"/>
      <path id="Path_320" data-name="Path 320" d="M898.037,467.219a7.748,7.748,0,0,1,.06-1.559,8.812,8.812,0,0,0-1.69,2.323,9.055,9.055,0,0,0-.537,1.319A8.043,8.043,0,0,1,898.037,467.219Z" transform="translate(-870.999 -465.66)"/>
      <path id="Path_321" data-name="Path 321" d="M833.07,595.229a8.35,8.35,0,0,1,1.043-1.195,8.105,8.105,0,0,1,.308-1.414,8.866,8.866,0,0,0-.81,1.312,9.05,9.05,0,0,0-.541,1.3Z" transform="translate(-830.401 -547.735)"/>
      <path id="Path_322" data-name="Path 322" d="M840.61,580.394a8.343,8.343,0,0,1,1.043-1.195,8.13,8.13,0,0,1,.293-1.379h0a9.083,9.083,0,0,0-.778,1.269A9.456,9.456,0,0,0,840.61,580.394Z" transform="translate(-835.275 -538.168)"/>
      <path id="Path_323" data-name="Path 323" d="M902.275,520.847a7.358,7.358,0,0,1-1.213.969,7.937,7.937,0,0,1-.223,2.4,6.644,6.644,0,0,1,.707.163,8.756,8.756,0,0,1,1.5-.647c3.6-1.181,7.237.074,8.131,2.8a4.13,4.13,0,0,1,.113,2.121,4.855,4.855,0,0,0,1.167-4.645c-.9-2.726-4.539-3.981-8.131-2.8a8.756,8.756,0,0,0-1.5.647c-.23-.064-.467-.12-.707-.163C902.187,521.409,902.236,521.13,902.275,520.847Z" transform="translate(-874.212 -501.283)"/>
      <path id="Path_324" data-name="Path 324" d="M826.874,607.49a8.865,8.865,0,0,0-.81,1.312,9.39,9.39,0,0,0-.544,1.322,8.653,8.653,0,0,1,1.061-1.2A8.1,8.1,0,0,1,826.874,607.49Z" transform="translate(-825.52 -557.348)"/>
      <path id="Path_325" data-name="Path 325" d="M887.127,488.689a7.779,7.779,0,0,1,.06-1.559,8.813,8.813,0,0,0-1.69,2.323,9.072,9.072,0,0,0-.537,1.319,8.044,8.044,0,0,1,2.167-2.082Z" transform="translate(-863.946 -479.54)"/>
      <path id="Path_326" data-name="Path 326" d="M874.754,577.122a8.583,8.583,0,0,1-.354,1.548,9.427,9.427,0,0,0,.739-1.216,9.013,9.013,0,0,0,.576-1.414A8.562,8.562,0,0,1,874.754,577.122Z" transform="translate(-857.119 -537.017)"/>
      <path id="Path_327" data-name="Path 327" d="M859.674,606.942a8.546,8.546,0,0,1-.354,1.548,9.072,9.072,0,0,0,.742-1.216,9.361,9.361,0,0,0,.576-1.414A8.335,8.335,0,0,1,859.674,606.942Z" transform="translate(-847.371 -556.295)"/>
      <path id="Path_328" data-name="Path 328" d="M867.214,592.061a8.518,8.518,0,0,1-.354,1.548,9.434,9.434,0,0,0,.757-1.195,9.018,9.018,0,0,0,.576-1.414A8.561,8.561,0,0,1,867.214,592.061Z" transform="translate(-852.245 -546.688)"/>
      <path id="Path_329" data-name="Path 329" d="M882.294,562.167a8.482,8.482,0,0,1-.354,1.545,9.146,9.146,0,0,0,.739-1.213,9.014,9.014,0,0,0,.58-1.439A8.217,8.217,0,0,1,882.294,562.167Z" transform="translate(-861.994 -527.333)"/>
      <path id="Path_330" data-name="Path 330" d="M830.351,521.464c2.729-.894,5.9,1.294,7.071,4.889a9.077,9.077,0,0,1,.354,1.594,7.485,7.485,0,0,1,1.57,1.672,9.027,9.027,0,0,1,.735-2.033c.124-.24.258-.47.4-.707a7.451,7.451,0,0,0-1.414-1.478,8.969,8.969,0,0,0-.354-1.591c-1.181-3.6-4.348-5.787-7.071-4.889a4.84,4.84,0,0,0-3.047,3.687A4.132,4.132,0,0,1,830.351,521.464Z" transform="translate(-827.505 -499.962)"/>
      <path id="Path_331" data-name="Path 331" d="M865.407,531.635a7.727,7.727,0,0,1,.064-1.556,8.743,8.743,0,0,0-1.693,2.323,8.864,8.864,0,0,0-.537,1.315A8.13,8.13,0,0,1,865.407,531.635Z" transform="translate(-849.905 -507.305)"/>
      <path id="Path_332" data-name="Path 332" d="M876.267,510.156a7.723,7.723,0,0,1,.064-1.556,8.666,8.666,0,0,0-1.693,2.319,9.064,9.064,0,0,0-.537,1.319,8.044,8.044,0,0,1,2.167-2.082Z" transform="translate(-856.925 -493.419)"/>
      <path id="Path_333" data-name="Path 333" d="M852.134,621.826a8.568,8.568,0,0,1-.354,1.552,9.1,9.1,0,0,0,1.319-2.648A8.335,8.335,0,0,1,852.134,621.826Z" transform="translate(-842.496 -565.908)"/>
    </g>
    <path id="Path_334" data-name="Path 334" class="cls-3" d="M921.478,408.115a8.373,8.373,0,0,0,2.91-3.295c1.736-3.429,1.018-7.286-1.6-8.612s-6.151.382-7.887,3.815a8.372,8.372,0,0,0-.926,4.306,8.708,8.708,0,0,0-3.839,7.59,8.348,8.348,0,0,0-2.91,3.295,8.732,8.732,0,0,0-.534,1.3,7.372,7.372,0,0,1,3.808-2.828,4.327,4.327,0,0,0,5.494,2.772,7.372,7.372,0,0,1,0,4.741,9,9,0,0,0,.735-1.2,8.372,8.372,0,0,0,.926-4.3,8.686,8.686,0,0,0,3.839-7.59Z" transform="translate(-857.833 -308.564)"/>
    <path id="Path_335" data-name="Path 335" class="cls-4" d="M949.7,204.865a25.453,25.453,0,0,0,3.083-1.708l-11.43-8.35,13.434,6.919a25.5,25.5,0,0,0,9.545-17.786l-22.831.233,22.881-3.825a25.454,25.454,0,1,0-50.407,6.463,25.485,25.485,0,0,0-4.688,2.828l11.932,16.591-14.325-14.523a25.483,25.483,0,0,0-7.131,23.2,25.454,25.454,0,1,0,35.724,18.066,25.454,25.454,0,0,0,14.212-28.1Z" transform="translate(-843.856 -153.974)"/>
    <path id="Path_336" data-name="Path 336" class="cls-5" d="M902.027,274.506a25.373,25.373,0,0,0-2.245,16.514,25.454,25.454,0,1,0,35.725,18.066C940.5,306.766,903.307,271.957,902.027,274.506Z" transform="translate(-843.868 -230.092)"/>
    <circle id="Ellipse_44" data-name="Ellipse 44" class="cls-4" cx="3.779" cy="3.779" r="3.779" transform="translate(83.065)"/>
    <circle id="Ellipse_45" data-name="Ellipse 45" class="cls-4" cx="3.779" cy="3.779" r="3.779" transform="translate(105.14 2.425)"/>
    <circle id="Ellipse_46" data-name="Ellipse 46" class="cls-4" cx="3.779" cy="3.779" r="3.779" transform="translate(113.653 36.386)"/>
    <circle id="Ellipse_47" data-name="Ellipse 47" class="cls-4" cx="3.779" cy="3.779" r="3.779" transform="translate(102.163 53.345)"/>
    <circle id="Ellipse_48" data-name="Ellipse 48" class="cls-4" cx="3.779" cy="3.779" r="3.779" transform="translate(86.466 88.567)"/>
    <circle id="Ellipse_49" data-name="Ellipse 49" class="cls-5" cx="3.779" cy="3.779" r="3.779" transform="translate(86.466 88.567)"/>
    <circle id="Ellipse_50" data-name="Ellipse 50" class="cls-4" cx="3.779" cy="3.779" r="3.779" transform="translate(51.565 48.674)"/>
    <circle id="Ellipse_51" data-name="Ellipse 51" class="cls-5" cx="3.779" cy="3.779" r="3.779" transform="translate(51.565 48.674)"/>
    <path id="Path_337" data-name="Path 337" class="cls-5" d="M908.2,458.361c-2.514-1.269-3.274-4.865-1.807-8.181-.078.134-.152.272-.223.417-1.736,3.429-1.018,7.283,1.6,8.609s6.151-.382,7.884-3.811c.074-.141.138-.286.2-.428C914.056,458.117,910.708,459.633,908.2,458.361Z" transform="translate(-856.883 -343.745)"/>
    <path id="Path_338" data-name="Path 338" class="cls-5" d="M897.638,479.234c-2.51-1.269-3.274-4.865-1.8-8.184-.078.138-.156.276-.226.417-1.736,3.429-1.018,7.286,1.6,8.609s6.151-.378,7.887-3.811c.071-.141.138-.283.2-.428C903.5,478.987,900.151,480.5,897.638,479.234Z" transform="translate(-850.057 -357.237)"/>
    <path id="Path_339" data-name="Path 339" class="cls-5" d="M887.079,500.111c-2.51-1.269-3.274-4.865-1.8-8.181-.078.134-.156.272-.226.417-1.736,3.429-1.015,7.283,1.605,8.609s6.148-.382,7.884-3.811c.071-.141.138-.286.2-.428C892.941,499.867,889.593,501.383,887.079,500.111Z" transform="translate(-843.232 -370.735)"/>
    <path id="Path_340" data-name="Path 340" class="cls-5" d="M876.556,521.041c-2.514-1.269-3.274-4.865-1.807-8.181-.078.134-.152.272-.226.414-1.732,3.429-1.015,7.286,1.6,8.612s6.151-.382,7.884-3.815c.074-.141.138-.283.2-.428C882.4,520.776,879.055,522.292,876.556,521.041Z" transform="translate(-836.428 -384.266)"/>
    <path id="Path_341" data-name="Path 341" class="cls-5" d="M865.967,541.861c-2.514-1.269-3.274-4.865-1.807-8.181-.078.134-.152.272-.223.417-1.736,3.429-1.018,7.283,1.6,8.609s6.151-.382,7.887-3.811c.071-.141.138-.286.2-.428C871.833,541.617,868.478,543.133,865.967,541.861Z" transform="translate(-829.583 -397.725)"/>
    <path id="Path_342" data-name="Path 342" class="cls-5" d="M855.408,562.741c-2.51-1.269-3.274-4.865-1.8-8.181-.078.134-.156.272-.226.414-1.736,3.429-1.018,7.286,1.6,8.612s6.134-.385,7.9-3.815c.071-.141.138-.283.2-.428C861.266,562.493,857.936,564.01,855.408,562.741Z" transform="translate(-822.757 -411.223)"/>
    <path id="Path_343" data-name="Path 343" class="cls-5" d="M844.852,583.611c-2.51-1.269-3.274-4.865-1.8-8.181-.078.138-.152.272-.226.417-1.732,3.429-1.015,7.283,1.605,8.609s6.151-.382,7.884-3.811c.074-.141.138-.286.2-.428C850.714,583.367,847.366,584.883,844.852,583.611Z" transform="translate(-815.935 -424.715)"/>
    <path id="Path_344" data-name="Path 344" class="cls-5" d="M834.3,604.491c-2.514-1.269-3.274-4.865-1.807-8.181q-.117.2-.223.414c-1.736,3.429-1.018,7.286,1.6,8.612s6.151-.382,7.887-3.815c.071-.141.134-.283.2-.428C840.156,604.243,836.808,605.746,834.3,604.491Z" transform="translate(-809.109 -438.213)"/>
    <path id="Path_345" data-name="Path 345" class="cls-5" d="M827.357,618.2c-2.51-1.273-3.274-4.865-1.8-8.184-.078.138-.156.276-.226.417-1.736,3.429-1.018,7.286,1.6,8.609s6.151-.378,7.887-3.811c.071-.141.138-.283.2-.428C833.216,617.957,829.868,619.473,827.357,618.2Z" transform="translate(-804.623 -447.076)"/>
    <path id="Path_346" data-name="Path 346" class="cls-4" d="M955.608,505.69a13.224,13.224,0,0,0,.707-3.461,1.888,1.888,0,1,0-.286-3.755,13.19,13.19,0,0,0-6.943-8.948,1.889,1.889,0,1,0-3.373-1.7,1.767,1.767,0,0,0-.17.506,13.254,13.254,0,0,0-11.621,3.482,13.307,13.307,0,0,0-3.472-.1l-1.531,6.968-.092-6.717a13.229,13.229,0,1,0,7.071,25.454,1.866,1.866,0,0,0,.8.764,1.888,1.888,0,0,0,2.588-2.432,13.205,13.205,0,0,0,1.7-1.386,13.24,13.24,0,0,0,13.9-7.011l-7.124-1.2Z" transform="translate(-865.423 -367.409)"/>
    <g id="Group_164" data-name="Group 164" class="cls-2" transform="translate(52.99 123.79)">
      <path id="Path_347" data-name="Path 347" d="M944.571,517.145c-2.63-3.472-4.242-7.583-6.1-11.529a48.217,48.217,0,0,0-2.5-4.723l-1.393-1.623c-.233.2-.467.4-.707.619a13.306,13.306,0,0,0-3.472-.1l-1.531,6.968-.092-6.717a13.229,13.229,0,1,0,7.071,25.454,1.866,1.866,0,0,0,.8.764,1.888,1.888,0,0,0,2.588-2.432,13.21,13.21,0,0,0,1.7-1.386,13.254,13.254,0,0,0,7.933-1.174A18.633,18.633,0,0,1,944.571,517.145Z" transform="translate(-918.381 -499.27)"/>
    </g>
    <path id="Path_348" data-name="Path 348" class="cls-4" d="M801.1,458.914a1.888,1.888,0,0,0-1.69-2.74,13.212,13.212,0,0,0-4.136-7.364,13.267,13.267,0,0,0,.389-4.136l-6.947-1.184,6.745-.424a13.434,13.434,0,0,0-.5-1.867,1.93,1.93,0,0,0,.622-.707,1.891,1.891,0,0,0-2.33-2.63,13.229,13.229,0,0,0-23.612,10.931,1.856,1.856,0,0,0-.94.884,1.888,1.888,0,0,0,2.386,2.606,13.268,13.268,0,0,0,2.507,3.058,13.222,13.222,0,0,0,.615,8.4l9.316-2.418-8.308,4.37a13.229,13.229,0,0,0,24.362-5.748A1.884,1.884,0,0,0,801.1,458.914Z" transform="translate(-768.494 -332.169)"/>
    <path id="Path_349" data-name="Path 349" class="cls-5" d="M809.657,487.939a1.888,1.888,0,0,0-1.69-2.74,13.212,13.212,0,0,0-4.136-7.364c.057-.223.106-.442.148-.665a4.66,4.66,0,0,0-.682.6c-1.255,1.375-1.9,3.224-3.139,4.614a9.043,9.043,0,0,1-5.844,2.726,22.25,22.25,0,0,1-6.551-.527,18.9,18.9,0,0,0-5.416-.59,3.024,3.024,0,0,0-.516.067c.106.1.212.205.322.3a13.222,13.222,0,0,0,.615,8.4l9.316-2.422-8.308,4.37a13.229,13.229,0,0,0,24.362-5.748A1.884,1.884,0,0,0,809.657,487.939Z" transform="translate(-777.055 -361.193)"/>
  </g>
  <g id="Group_168" data-name="Group 168" class="cls-2" transform="translate(0 112.915)">
    <path id="Path_350" data-name="Path 350" class="cls-3" d="M176.831,538.609c.453-1.375,2.287-2.008,4.1-1.414a4.514,4.514,0,0,1,.753.325c.117-.032.237-.06.354-.081a3.98,3.98,0,0,1-.113-1.209,4.405,4.405,0,0,1-1.934-3.825,4.393,4.393,0,0,1-1.934-3.822,4.243,4.243,0,0,1-1.464-1.662c-.87-1.725-.516-3.666.81-4.338s3.1.194,3.974,1.92a4.243,4.243,0,0,1,.467,2.167,4.373,4.373,0,0,1,1.934,3.822,4.384,4.384,0,0,1,1.934,3.825,4.373,4.373,0,0,1,1.934,3.822,4.243,4.243,0,0,1,1.467,1.662,4.477,4.477,0,0,1,.354,1.025,3.769,3.769,0,0,1,.788-.845,4.446,4.446,0,0,1,.184-.8c.6-1.81,2.192-2.913,3.567-2.475s2,2.287,1.414,4.1a4.126,4.126,0,0,1-1.347,1.976,4.479,4.479,0,0,1-.184.8,3.924,3.924,0,0,1-1.807,2.27,4.537,4.537,0,0,1,.148.707,4.444,4.444,0,0,1,.894,1.213,4.355,4.355,0,0,1,.445,1.442,4.436,4.436,0,0,1,.9,1.213,4.511,4.511,0,0,0,1.343,2.659c.873,1.725.513,3.67-.806,4.334s-3.1-.191-3.974-1.92a4.412,4.412,0,0,1-.445-1.442,650305.115,650305.115,0,0,1-2.687-5.31,4.454,4.454,0,0,1-.9-1.213,4.5,4.5,0,0,1-.445-1.442,4.444,4.444,0,0,1-.894-1.213,4.355,4.355,0,0,1-.445-1.442,4.11,4.11,0,0,1-.6-.707,4.207,4.207,0,0,1-2.185-.134,4.7,4.7,0,0,1-.757-.329,4.105,4.105,0,0,1-2.386-.085C177.488,541.579,176.382,539.985,176.831,538.609Z" transform="translate(-145.193 -470.784)"/>
    <g id="Group_166" data-name="Group 166" class="cls-2" transform="translate(31.554 56.378)">
      <path id="Path_351" data-name="Path 351" d="M187.17,553a3.793,3.793,0,0,0,.615.491,4,4,0,0,0,.2,1.5,4.217,4.217,0,0,1-.375-.612A4.4,4.4,0,0,1,187.17,553Z" transform="translate(-183.894 -546.972)"/>
      <path id="Path_352" data-name="Path 352" d="M181.7,542.2a3.963,3.963,0,0,0,.615.488,4.027,4.027,0,0,0,.2,1.5A4.49,4.49,0,0,1,181.7,542.2Z" transform="translate(-180.357 -539.99)"/>
      <path id="Path_353" data-name="Path 353" d="M194.078,536.735a3.928,3.928,0,0,0-.028-.785,4.476,4.476,0,0,1,.852,1.17,4.593,4.593,0,0,1,.269.665A4.037,4.037,0,0,0,194.078,536.735Z" transform="translate(-188.341 -535.95)"/>
      <path id="Path_354" data-name="Path 354" d="M227.589,601.238a4.119,4.119,0,0,0-.523-.6,4.383,4.383,0,0,0-.156-.707,4.677,4.677,0,0,1,.407.658,4.6,4.6,0,0,1,.272.651Z" transform="translate(-209.584 -577.311)"/>
      <path id="Path_355" data-name="Path 355" d="M223.772,593.728a1.508,1.508,0,0,1-.672-1.308h0a4.48,4.48,0,0,1,.665,1.3Z" transform="translate(-207.121 -572.456)"/>
      <path id="Path_356" data-name="Path 356" d="M183.116,563.749a3.578,3.578,0,0,0,.612.484,3.853,3.853,0,0,0,.113,1.209,3.54,3.54,0,0,0-.354.081,4.684,4.684,0,0,0-.757-.325c-1.81-.594-3.645.039-4.094,1.414a2.058,2.058,0,0,0-.06,1.061,2.45,2.45,0,0,1-.587-2.337c.453-1.375,2.287-2.008,4.1-1.414a4.514,4.514,0,0,1,.753.325c.117-.032.237-.06.354-.081a3.647,3.647,0,0,1-.078-.417Z" transform="translate(-177.902 -553.889)"/>
      <path id="Path_357" data-name="Path 357" d="M230.71,607.4a4.523,4.523,0,0,1,.407.661,4.6,4.6,0,0,1,.272.665,4.15,4.15,0,0,0-.523-.6A4.384,4.384,0,0,0,230.71,607.4Z" transform="translate(-212.041 -582.14)"/>
      <path id="Path_358" data-name="Path 358" d="M199.548,547.565a3.917,3.917,0,0,0-.028-.785,4.416,4.416,0,0,1,.852,1.17,4.549,4.549,0,0,1,.269.661A4.024,4.024,0,0,0,199.548,547.565Z" transform="translate(-191.877 -542.951)"/>
      <path id="Path_359" data-name="Path 359" d="M206.611,592.085a4.221,4.221,0,0,0,.173.778,4.465,4.465,0,0,1-.665-1.333,4.139,4.139,0,0,0,.491.555Z" transform="translate(-196.144 -571.881)"/>
      <path id="Path_360" data-name="Path 360" d="M214.208,607.131a4.477,4.477,0,0,0,.177.778,4.217,4.217,0,0,1-.375-.612,4.553,4.553,0,0,1-.29-.707A4.6,4.6,0,0,0,214.208,607.131Z" transform="translate(-201.057 -581.616)"/>
      <path id="Path_361" data-name="Path 361" d="M210.411,599.621a4.223,4.223,0,0,0,.173.778,4.553,4.553,0,0,1-.375-.612,4.454,4.454,0,0,1-.29-.707,4.144,4.144,0,0,0,.491.541Z" transform="translate(-198.601 -576.761)"/>
      <path id="Path_362" data-name="Path 362" d="M202.844,584.6a4.192,4.192,0,0,0,.173.781,4.4,4.4,0,0,1-.354-.612,4.324,4.324,0,0,1-.293-.707A3.935,3.935,0,0,0,202.844,584.6Z" transform="translate(-193.72 -567.052)"/>
      <path id="Path_363" data-name="Path 363" d="M219.239,564.074c-1.375-.453-2.97.651-3.564,2.475a4.45,4.45,0,0,0-.184.8,3.759,3.759,0,0,0-.792.841,4.535,4.535,0,0,0-.371-1.025c-.06-.12-.127-.237-.2-.354a3.757,3.757,0,0,1,.707-.742,4.449,4.449,0,0,1,.184-.8c.6-1.81,2.192-2.913,3.567-2.475a2.45,2.45,0,0,1,1.534,1.86A2.1,2.1,0,0,0,219.239,564.074Z" transform="translate(-201.322 -553.242)"/>
      <path id="Path_364" data-name="Path 364" d="M210.488,569.185a3.928,3.928,0,0,0-.028-.785,4.416,4.416,0,0,1,.852,1.17,4.6,4.6,0,0,1,.269.665,3.989,3.989,0,0,0-1.092-1.05Z" transform="translate(-198.95 -556.928)"/>
      <path id="Path_365" data-name="Path 365" d="M205.018,558.365a3.934,3.934,0,0,0-.028-.785,4.536,4.536,0,0,1,.852,1.17,4.6,4.6,0,0,1,.269.665,4.036,4.036,0,0,0-1.092-1.05Z" transform="translate(-195.414 -549.933)"/>
      <path id="Path_366" data-name="Path 366" d="M218.008,614.641a4.208,4.208,0,0,0,.177.781,4.458,4.458,0,0,1-.375-.615,4.556,4.556,0,0,1-.29-.707A4.593,4.593,0,0,0,218.008,614.641Z" transform="translate(-203.514 -586.471)"/>
    </g>
    <path id="Path_367" data-name="Path 367" class="cls-3" d="M167.122,506.961a4.214,4.214,0,0,1-1.464-1.658c-.873-1.729-.513-3.67.806-4.338s3.1.191,3.974,1.92a4.218,4.218,0,0,1,.467,2.164,4.384,4.384,0,0,1,1.934,3.825,4.242,4.242,0,0,1,1.467,1.658,4.678,4.678,0,0,1,.269.658,3.737,3.737,0,0,0-1.92-1.414,2.178,2.178,0,0,1-2.768,1.414,3.719,3.719,0,0,0,0,2.386,4.662,4.662,0,0,1-.354-.6,4.243,4.243,0,0,1-.47-2.164,4.394,4.394,0,0,1-1.934-3.825Z" transform="translate(-138.125 -456.809)"/>
    <path id="Path_368" data-name="Path 368" class="cls-4" d="M97.032,404.274a13.008,13.008,0,0,1-1.552-.859l5.756-4.207-6.767,3.482a12.847,12.847,0,0,1-4.8-8.959l11.5.12-11.529-1.93a12.83,12.83,0,1,1,25.387,3.253,12.986,12.986,0,0,1,2.362,1.414l-6.01,8.358,7.216-7.311a12.84,12.84,0,0,1,3.592,11.684,12.83,12.83,0,1,1-17.995,9.1,12.826,12.826,0,0,1-7.159-14.141Z" transform="translate(-89.29 -378.636)"/>
    <path id="Path_369" data-name="Path 369" class="cls-5" d="M147.2,439.657a12.77,12.77,0,0,1,1.131,8.319,12.83,12.83,0,1,1-17.995,9.1C127.815,455.906,146.552,438.374,147.2,439.657Z" transform="translate(-115.434 -417.279)"/>
    <circle id="Ellipse_52" data-name="Ellipse 52" class="cls-4" cx="1.906" cy="1.906" r="1.906" transform="translate(15.407)"/>
    <circle id="Ellipse_53" data-name="Ellipse 53" class="cls-4" cx="1.906" cy="1.906" r="1.906" transform="translate(4.285 1.223)"/>
    <circle id="Ellipse_54" data-name="Ellipse 54" class="cls-4" cx="1.906" cy="1.906" r="1.906" transform="translate(0 18.331)"/>
    <circle id="Ellipse_55" data-name="Ellipse 55" class="cls-4" cx="1.906" cy="1.906" r="1.906" transform="translate(5.787 26.876)"/>
    <circle id="Ellipse_56" data-name="Ellipse 56" class="cls-4" cx="1.906" cy="1.906" r="1.906" transform="translate(13.692 44.619)"/>
    <circle id="Ellipse_57" data-name="Ellipse 57" class="cls-5" cx="1.906" cy="1.906" r="1.906" transform="translate(13.692 44.619)"/>
    <circle id="Ellipse_58" data-name="Ellipse 58" class="cls-4" cx="1.906" cy="1.906" r="1.906" transform="translate(31.274 24.521)"/>
    <circle id="Ellipse_59" data-name="Ellipse 59" class="cls-5" cx="1.906" cy="1.906" r="1.906" transform="translate(31.274 24.521)"/>
    <path id="Path_370" data-name="Path 370" class="cls-5" d="M181.191,532.272c1.266-.64,1.647-2.45.909-4.122.039.071.078.138.113.209.873,1.729.513,3.67-.81,4.338s-3.1-.191-3.97-1.92c-.035-.071-.071-.141-.1-.216C178.239,532.148,179.925,532.912,181.191,532.272Z" transform="translate(-145.979 -474.53)"/>
    <path id="Path_371" data-name="Path 371" class="cls-5" d="M186.507,542.792c1.266-.64,1.651-2.454.912-4.122.039.067.078.138.113.209.873,1.729.513,3.67-.81,4.338s-3.1-.191-3.97-1.92c-.039-.071-.071-.145-.1-.216C183.559,542.676,185.248,543.432,186.507,542.792Z" transform="translate(-149.418 -481.33)"/>
    <path id="Path_372" data-name="Path 372" class="cls-5" d="M191.827,553.3c1.266-.64,1.651-2.45.909-4.122.042.071.078.138.117.212.873,1.725.509,3.67-.81,4.334s-3.1-.191-3.97-1.92c-.039-.071-.071-.141-.1-.216C188.875,553.178,190.561,553.942,191.827,553.3Z" transform="translate(-152.857 -488.125)"/>
    <path id="Path_373" data-name="Path 373" class="cls-5" d="M197.147,563.822c1.266-.64,1.651-2.45.909-4.122.039.067.078.138.113.209.877,1.729.513,3.67-.806,4.338s-3.1-.191-3.974-1.92c-.035-.071-.067-.145-.1-.216C194.2,563.695,195.881,564.462,197.147,563.822Z" transform="translate(-156.296 -494.926)"/>
    <path id="Path_374" data-name="Path 374" class="cls-5" d="M202.461,574.339c1.266-.64,1.647-2.45.909-4.119.039.067.078.138.113.209.873,1.729.513,3.67-.806,4.338s-3.1-.194-3.974-1.92c-.035-.074-.067-.145-.1-.216C199.509,574.215,201.195,574.979,202.461,574.339Z" transform="translate(-159.729 -501.726)"/>
    <path id="Path_375" data-name="Path 375" class="cls-5" d="M207.781,584.852c1.266-.64,1.647-2.45.909-4.122.039.071.078.138.113.209.873,1.729.513,3.67-.806,4.338s-3.1-.191-3.974-1.92c-.035-.071-.071-.141-.1-.216C204.829,584.714,206.515,585.492,207.781,584.852Z" transform="translate(-163.168 -508.521)"/>
    <path id="Path_376" data-name="Path 376" class="cls-5" d="M213.1,595.372c1.262-.64,1.647-2.453.909-4.122.039.067.078.138.113.209.873,1.729.513,3.67-.81,4.338s-3.1-.194-3.97-1.92c-.035-.074-.071-.145-.1-.216C210.149,595.245,211.835,596.012,213.1,595.372Z" transform="translate(-166.607 -515.322)"/>
    <path id="Path_377" data-name="Path 377" class="cls-5" d="M218.417,605.882c1.266-.64,1.651-2.45.909-4.122.042.071.078.138.117.212.873,1.725.509,3.67-.81,4.334s-3.1-.191-3.97-1.92c-.039-.071-.071-.141-.1-.216C215.469,605.758,217.151,606.522,218.417,605.882Z" transform="translate(-170.047 -522.116)"/>
    <path id="Path_378" data-name="Path 378" class="cls-5" d="M221.941,612.792c1.266-.64,1.647-2.45.909-4.122.039.071.078.138.113.212.873,1.725.513,3.67-.806,4.334s-3.1-.191-3.974-1.92c-.035-.071-.071-.141-.1-.216C218.978,612.676,220.664,613.432,221.941,612.792Z" transform="translate(-172.322 -526.583)"/>
    <path id="Path_379" data-name="Path 379" class="cls-4" d="M130.481,556.114a6.565,6.565,0,0,1-.354-1.743.951.951,0,1,1,.145-1.891,6.653,6.653,0,0,1,3.5-4.508.953.953,0,1,1,1.7-.859.863.863,0,0,1,.085.255,6.689,6.689,0,0,1,5.855,1.768,6.606,6.606,0,0,1,1.768-.049l.771,3.507.018-3.383a6.664,6.664,0,1,1-3.564,12.83.975.975,0,0,1-.4.385.955.955,0,0,1-1.308-1.227,6.661,6.661,0,0,1-.852-.707,6.671,6.671,0,0,1-7-3.535l3.588-.6Z" transform="translate(-114.851 -486.45)"/>
    <g id="Group_167" data-name="Group 167" class="cls-2" transform="translate(18.992 62.363)">
      <path id="Path_380" data-name="Path 380" d="M144.551,561.885a32.082,32.082,0,0,0,3.072-5.805,23.583,23.583,0,0,1,1.259-2.383l.707-.817c.12.1.237.205.354.311a6.606,6.606,0,0,1,1.768-.049l.771,3.507.011-3.373a6.664,6.664,0,1,1-3.564,12.83.975.975,0,0,1-.4.385.955.955,0,0,1-1.308-1.227,6.659,6.659,0,0,1-.852-.707,6.717,6.717,0,0,1-4-.59,9.4,9.4,0,0,0,2.181-2.082Z" transform="translate(-142.37 -552.88)"/>
    </g>
    <path id="Path_381" data-name="Path 381" class="cls-4" d="M214.722,532.563a.948.948,0,0,1,.421-1.276.933.933,0,0,1,.431-.1,6.664,6.664,0,0,1,2.082-3.712,6.615,6.615,0,0,1-.194-2.082l3.5-.587-3.4-.216a6.594,6.594,0,0,1,.255-.94.933.933,0,0,1-.315-.354.955.955,0,0,1,1.174-1.326,6.587,6.587,0,0,1,2.429-2.1,6.661,6.661,0,0,1,9.464,7.6.951.951,0,0,1-.728,1.757,6.624,6.624,0,0,1-1.262,1.534,6.682,6.682,0,0,1-.308,4.242l-4.677-1.23,4.182,2.2a6.668,6.668,0,0,1-11.607-.544,6.756,6.756,0,0,1-.686-2.351A.944.944,0,0,1,214.722,532.563Z" transform="translate(-170.085 -468.712)"/>
    <path id="Path_382" data-name="Path 382" class="cls-5" d="M214.722,547.173a.947.947,0,0,1,.421-1.276.933.933,0,0,1,.431-.1,6.664,6.664,0,0,1,2.082-3.712c-.028-.11-.053-.223-.074-.332a2.57,2.57,0,0,1,.354.3c.633.707.958,1.623,1.58,2.323a4.564,4.564,0,0,0,2.945,1.375,11.15,11.15,0,0,0,3.3-.265,9.668,9.668,0,0,1,2.729-.3l.262.035-.163.152a6.682,6.682,0,0,1-.308,4.242l-4.688-1.23,4.182,2.2a6.668,6.668,0,0,1-11.607-.544,6.756,6.756,0,0,1-.686-2.351A.944.944,0,0,1,214.722,547.173Z" transform="translate(-170.085 -483.322)"/>
  </g>
  <path id="Path_383" data-name="Path 383" class="cls-1" d="M395.785,91.578a7.152,7.152,0,0,0-4.15.453,6.123,6.123,0,0,1-4.985,0,6.986,6.986,0,0,0-5.862.117,3.634,3.634,0,0,1-1.686.421c-2.376,0-4.352-2.393-4.762-5.55a4.621,4.621,0,0,0,1.188-1.28,6.679,6.679,0,0,1,11.921-.042,4.6,4.6,0,0,0,3.945,2.171h.064C393.332,87.855,394.969,89.372,395.785,91.578Z" transform="translate(-273.339 -73.226)"/>
  <path id="Path_384" data-name="Path 384" class="cls-1" d="M402.979,65.543l-3.839,2.436,2.33-4.242a3.8,3.8,0,0,0-2.323-.827h-.06a4.556,4.556,0,0,1-.8-.06l-1.3.827.559-1.015a4.663,4.663,0,0,1-2.277-1.715l-2.337,1.46,1.471-2.676a6.815,6.815,0,0,0-5.218-2.641,7.14,7.14,0,0,0-5.975,3.687A4.462,4.462,0,0,1,379.26,62.9h-.117c-2.673,0-4.843,3.03-4.843,6.774s2.171,6.77,4.843,6.77a3.634,3.634,0,0,0,1.686-.421,6.972,6.972,0,0,1,5.862-.113,6.141,6.141,0,0,0,4.985,0,6.982,6.982,0,0,1,5.809.113,3.659,3.659,0,0,0,1.669.41c2.676,0,4.843-3.03,4.843-6.77a8.612,8.612,0,0,0-1.018-4.119Z" transform="translate(-273.313 -57.09)"/>
  <path id="Path_385" data-name="Path 385" class="cls-1" d="M152.977,222.612a10.253,10.253,0,0,0-5.929.643,8.736,8.736,0,0,1-7.12-.018,9.977,9.977,0,0,0-8.372.166,5.215,5.215,0,0,1-2.411.6c-3.394,0-6.219-3.419-6.806-7.93a6.508,6.508,0,0,0,1.7-1.828c1.99-3.207,5.073-5.268,8.534-5.268s6.505,2.036,8.485,5.208a6.547,6.547,0,0,0,5.657,3.1h.088C149.481,217.285,151.821,219.452,152.977,222.612Z" transform="translate(-110.429 -155.282)"/>
  <path id="Path_386" data-name="Path 386" class="cls-1" d="M163.236,185.413l-5.487,3.479,3.33-6.056a5.423,5.423,0,0,0-3.32-1.184h-.088a6.419,6.419,0,0,1-1.138-.085l-1.852,1.2.8-1.449a6.611,6.611,0,0,1-3.253-2.475l-3.33,2.121,2.1-3.854a9.743,9.743,0,0,0-7.456-3.769c-3.461,0-6.544,2.058-8.534,5.264a6.381,6.381,0,0,1-5.657,3.044h-.187c-3.822,0-6.919,4.331-6.919,9.676s3.1,9.676,6.919,9.676a5.165,5.165,0,0,0,2.411-.6,9.977,9.977,0,0,1,8.372-.163,8.736,8.736,0,0,0,7.12.018,9.987,9.987,0,0,1,8.3.159,5.179,5.179,0,0,0,2.383.59c3.822,0,6.919-4.334,6.919-9.676A12.289,12.289,0,0,0,163.236,185.413Z" transform="translate(-110.365 -132.242)"/>
  <path id="Path_387" data-name="Path 387" class="cls-1" d="M836.62,130.545a7.156,7.156,0,0,1,4.151.452,6.112,6.112,0,0,0,4.981,0,6.986,6.986,0,0,1,5.862.117,3.644,3.644,0,0,0,1.686.421c2.376,0,4.356-2.393,4.766-5.55a4.572,4.572,0,0,1-1.188-1.28,6.677,6.677,0,0,0-11.928-.06A4.6,4.6,0,0,1,841,126.826h-.06C839.056,126.823,837.415,128.339,836.62,130.545Z" transform="translate(-572.188 -98.406)"/>
  <path id="Path_388" data-name="Path 388" class="cls-1" d="M814.316,104.575,818.155,107l-2.333-4.242a3.8,3.8,0,0,1,2.323-.827h.064a4.6,4.6,0,0,0,.8-.06l1.3.827-.559-1.015a4.67,4.67,0,0,0,2.3-1.708l2.33,1.478L822.9,98.781a6.794,6.794,0,0,1,5.222-2.641,7.14,7.14,0,0,1,5.975,3.687,4.458,4.458,0,0,0,3.949,2.121h.127c2.676,0,4.843,3.03,4.843,6.774s-2.167,6.77-4.843,6.77a3.656,3.656,0,0,1-1.686-.421,6.972,6.972,0,0,0-5.862-.113,6.134,6.134,0,0,1-4.981,0,6.989,6.989,0,0,0-5.812.113,3.648,3.648,0,0,1-1.669.41c-2.673,0-4.843-3.03-4.843-6.77a8.612,8.612,0,0,1,1-4.136Z" transform="translate(-557.124 -82.335)"/>
  <ellipse id="Ellipse_60" data-name="Ellipse 60" class="cls-1" cx="40.656" cy="7.071" rx="40.656" ry="7.071" transform="translate(176.989 260.512)"/>
  <path id="Path_389" data-name="Path 389" class="cls-6" d="M440.246,608.708l-63-.81.187-1.616,3.044-26.653h55.324l4.069,26.653.311,2.022Z" transform="translate(-243.88 -374.712)"/>
  <path id="Path_390" data-name="Path 390" class="cls-5" d="M440.186,657.042H408.75l-31.5-.407.187-1.616h62.438Z" transform="translate(-243.88 -423.449)"/>
  <rect id="Rectangle_1648" data-name="Rectangle 1648" class="cls-6" width="83.593" height="2.019" transform="translate(122.87 231.978)"/>
  <path id="Path_391" data-name="Path 391" class="cls-3" d="M452.23,174.275A5.257,5.257,0,0,0,447,169H213.552a5.257,5.257,0,0,0-5.232,5.282v141.5H452.23Z" transform="translate(-166.013 -129.436)"/>
  <path id="Path_392" data-name="Path 392" class="cls-6" d="M208.32,580.19v16.574A5.232,5.232,0,0,0,213.552,602H447a5.232,5.232,0,0,0,5.232-5.232V580.19Z" transform="translate(-166.013 -395.257)"/>
  <rect id="Rectangle_1649" data-name="Rectangle 1649" class="cls-7" width="224.928" height="127.205" transform="translate(52.404 48.438)"/>
  <path id="Path_393" data-name="Path 393" class="cls-8" d="M546.755,604.133a5.444,5.444,0,0,0,4.288-2.082h0a5.677,5.677,0,0,0,.424-.622l-2.994-.506,3.235.021a5.452,5.452,0,0,0,.1-4.317l-4.338,2.249,4-2.941a5.451,5.451,0,1,0-9,6.1h0a5.444,5.444,0,0,0,4.285,2.1Z" transform="translate(-381.28 -403.683)"/>
  <path id="Path_394" data-name="Path 394" class="cls-5" d="M483.48,584.77l25.133,25.242-3.857-25.242Z" transform="translate(-312.554 -378.034)"/>
  <rect id="Rectangle_1650" data-name="Rectangle 1650" class="cls-9" width="225.172" height="7.431" transform="translate(52.227 48.452)"/>
  <rect id="Rectangle_1651" data-name="Rectangle 1651" class="cls-10" width="67.843" height="4.522" rx="0.58" transform="translate(130.408 49.746)"/>
  <path id="Path_395" data-name="Path 395" class="cls-3" d="M639,201.389h-.166l-.06-.053a1.375,1.375,0,0,0,.318-.88,1.336,1.336,0,1,0-1.333,1.347,1.379,1.379,0,0,0,.88-.322l.06.053v.166l1.036,1.036.308-.311Zm-1.241,0a.926.926,0,1,1,.66-.273.933.933,0,0,1-.66.273Z" transform="translate(-442.765 -148.914)"/>
  <circle id="Ellipse_61" data-name="Ellipse 61" class="cls-11" cx="1.361" cy="1.361" r="1.361" transform="translate(54.914 50.806)"/>
  <circle id="Ellipse_62" data-name="Ellipse 62" class="cls-12" cx="1.361" cy="1.361" r="1.361" transform="translate(58.658 50.806)"/>
  <circle id="Ellipse_63" data-name="Ellipse 63" class="cls-13" cx="1.361" cy="1.361" r="1.361" transform="translate(62.406 50.806)"/>
  <path id="Path_396" data-name="Path 396" class="cls-8" d="M542.853,336.259a15.092,15.092,0,0,0-25.073,0,13.534,13.534,0,0,0,25.073,0Z" transform="translate(-366.068 -233.239)"/>
  <path id="Path_397" data-name="Path 397" class="cls-8" d="M495.786,346.337a3.241,3.241,0,0,1-.622-.354,2.5,2.5,0,0,1-.686-.887,2.538,2.538,0,1,0-4.143.615,2.83,2.83,0,0,0,.6.484,1.747,1.747,0,0,1,.675.707,5.105,5.105,0,0,0,1.174,1.541,10.249,10.249,0,0,0,1.609,1.131,23.442,23.442,0,0,0-1.322,6.915,9.564,9.564,0,0,0-2.694.435,4.344,4.344,0,0,0-1.248.622,2.988,2.988,0,0,0-.308-.021,2.542,2.542,0,1,0,2.379,3.436,1.623,1.623,0,0,1,1.312-1.061,5.916,5.916,0,0,1,.654-.042,22.767,22.767,0,0,0,3.023,9.429,7.417,7.417,0,0,0-2.811,3.839,1.059,1.059,0,0,0-.2.145,2.409,2.409,0,0,0-.3.276,2.538,2.538,0,1,0,4.26.831,1.665,1.665,0,0,1,.414-1.8,7.116,7.116,0,0,1,.6-.516c2.867,3.348,6.279,5.494,10.539,5.748V355.1A15.679,15.679,0,0,1,495.786,346.337Zm35.293,11.214a2.808,2.808,0,0,0-.308.021,4.793,4.793,0,0,0-1.248-.622,9.5,9.5,0,0,0-2.694-.435,23.956,23.956,0,0,0-1.322-6.912,10.254,10.254,0,0,0,1.609-1.131,5.508,5.508,0,0,0,1.216-1.616,1.492,1.492,0,0,1,.626-.619h0a2.538,2.538,0,1,0-3.535-1.121,2.41,2.41,0,0,1-.686.887,6.664,6.664,0,0,1-.622.456,15.571,15.571,0,0,1-12.9,8.658v22.736c4.242-.255,7.675-2.411,10.539-5.748.223.177.41.336.569.484a1.718,1.718,0,0,1,.445,1.849,2.538,2.538,0,1,0,4.271-.824,2.407,2.407,0,0,0-.3-.276c-.117-.092-.2-.145-.2-.145a7.315,7.315,0,0,0-2.828-3.839,22.769,22.769,0,0,0,3.023-9.429,5.616,5.616,0,0,1,.626.042,1.7,1.7,0,0,1,1.361,1.061,2.538,2.538,0,0,0,4.9-.771,2.577,2.577,0,0,0-2.542-2.708Z" transform="translate(-345.704 -240.917)"/>
  <path id="Path_398" data-name="Path 398" class="cls-14" d="M660.156,603.805a3.38,3.38,0,0,0-.767-.852,10.192,10.192,0,0,1-2.644-5.7,58.167,58.167,0,0,0-1.1-6.3,20.773,20.773,0,0,0-1.244-3.065c-.354-.757-.8-2.238-1.446-2.768a.954.954,0,0,0-.445-.216,5.249,5.249,0,0,0-1.28-1.7,12.993,12.993,0,0,1-2.92-2.828,6.363,6.363,0,0,0-.682-1.163c-.456-.5-1.177-.707-1.6-1.237a3.555,3.555,0,0,1-.354-2.542c.071-.873,0-1.92-.757-2.379-.453-.279-1.036-.251-1.506-.5a2.18,2.18,0,0,1-.972-2.04c.014-.8.262-1.57.325-2.365.2-2.531-1.471-5.048-.827-7.5a15.256,15.256,0,0,1,1.216-2.6,5.424,5.424,0,0,0,.354-.9,12.634,12.634,0,0,0,3.443.831c-1.948-4.37-1.453-9.446-2.362-14.141l.117-.159a7.368,7.368,0,0,0,1.492-4.193c0-1.746-.817-3.38-1.184-5.091-.315-1.453-.3-2.973-.707-4.4a13.312,13.312,0,0,0-1.117-2.552q-.661-1.227-1.389-2.415-.679-2.248-1.361-4.5a21.734,21.734,0,0,0-2.528-6.06c-.453-.665-.59-2.015-1.287-2.418a3.114,3.114,0,0,0-1.838-.322,3.437,3.437,0,0,1-1.969-2.436,2.361,2.361,0,0,1,.071-.707H633c1.556-.141,2.344-1.513,3.154-2.715,1.308-1.944.81-4.575,0-6.788a4.6,4.6,0,0,0-1.061-1.87c-1.135-1.061-2.931-.827-4.394-1.34-.491-.17-.951-.428-1.453-.548a4.348,4.348,0,0,0-2.881.541,28.658,28.658,0,0,1-2.655,1.382c-.933.354-2.086.633-2.316,1.591a1.767,1.767,0,0,0,.078.965,3.6,3.6,0,0,0,1.092,1.577,6.3,6.3,0,0,0,3.843,8.775q.4.647.75,1.319a4.349,4.349,0,0,1,.615,2.574,7.93,7.93,0,0,0-.707.866,16.661,16.661,0,0,0-2.121,4.727,50.08,50.08,0,0,0-1.708,6.459,48.519,48.519,0,0,0-2.542,5.515,12.449,12.449,0,0,1-3.316-.445l-2.984-.675a6.745,6.745,0,0,1-2.934-1.177c-.431-.354-.767-.824-1.206-1.174s-1.319-.76-1.8-.47a1.414,1.414,0,0,0-.244.187c-.438-.887-.856-1.768-1.365-2.627a4.983,4.983,0,0,0-3.2-2.531,3.256,3.256,0,0,0-2.4.559,1.934,1.934,0,0,0-.707,2.574,4.694,4.694,0,0,0,.491.5,10.324,10.324,0,0,1,1.092,1.517,7.746,7.746,0,0,0,4.769,3.1l-.124.354a5.447,5.447,0,0,0-.651,2.386l13.272,4.543a8.177,8.177,0,0,0,2.673.59,4.318,4.318,0,0,0,1.061-.141v.707c.035,4.663-.707,9.294-.845,13.954a3.619,3.619,0,0,0,.537,2.432,2.723,2.723,0,0,0,2.6.707,6.044,6.044,0,0,0,.658-.148,24.5,24.5,0,0,1,.679,3.574l.877,6.314a9.589,9.589,0,0,1,.138,1.959,13.807,13.807,0,0,1-1.449,4.175,4.942,4.942,0,0,0-.159,4.285c.233.4.555.732.764,1.142a4.646,4.646,0,0,1,.354,2.008q.081,2.856.156,5.72a.647.647,0,0,0-.445.088c-.735.573-.654,3.3-.707,4.143-.057,1.414,0,2.857-.078,4.285a25.529,25.529,0,0,1-.923,5.688,3.638,3.638,0,0,1-.81,1.63,3.443,3.443,0,0,1-1.439.746,16.434,16.434,0,0,1-5.448.742,5.362,5.362,0,0,0-2.217.187,1.527,1.527,0,0,0-1,1.768,2.23,2.23,0,0,0,1.46,1.1,23.168,23.168,0,0,1,3.889,1.789,10.5,10.5,0,0,0,1.5.852,7.742,7.742,0,0,0,1.548.382l6.268,1.082c1.283.219,2.828.354,3.645-.707a3.213,3.213,0,0,0,.484-2.079,27.775,27.775,0,0,1-.134-4.267,13.131,13.131,0,0,1,.658-2.45q.937-2.782,1.881-5.561a29.229,29.229,0,0,0,1.51-5.688,2.12,2.12,0,0,0-.46-1.955,1.252,1.252,0,0,0-.467-.24c-.021-1.7-.244-3.412-.148-5.116.025-.424.067-.848.1-1.276a6.225,6.225,0,0,1,.873,1.032c.6.947.827,2.2,1.75,2.828.785.537,1.948.509,2.5,1.287a3.627,3.627,0,0,1,.4.958A6.83,6.83,0,0,0,643.038,589a11.314,11.314,0,0,0,.873,2.241q.781,1.651,1.556,3.291a38.38,38.38,0,0,0,1.768,3.454,9.741,9.741,0,0,1,1.184,2.284,5.246,5.246,0,0,1-1.6,4.949,13.768,13.768,0,0,1-4.656,2.715,1.31,1.31,0,0,0-.474.283.915.915,0,0,0-.216.615,2,2,0,0,0,1.442,1.768,5.4,5.4,0,0,0,2.415.088,35.86,35.86,0,0,0,13.017-3.846,4.6,4.6,0,0,0,1.552-1.2A1.814,1.814,0,0,0,660.156,603.805Z" transform="translate(-419.4 -340.345)"/>
  <path id="Path_399" data-name="Path 399" class="cls-15" d="M708.494,571.583q1.131,1.736,2.121,3.557a13.079,13.079,0,0,1,1.121,2.535c.41,1.414.4,2.924.707,4.366.354,1.7,1.181,3.316,1.181,5.056a7.27,7.27,0,0,1-1.492,4.158,19.736,19.736,0,0,1-3.118,3.217,66.33,66.33,0,0,1-4.412-21.64,7.592,7.592,0,0,1,.332-3.04c.23-.6,1.333-2.248,2.043-1.6.258.237.325,1.3.484,1.662A12.779,12.779,0,0,0,708.494,571.583Z" transform="translate(-486.832 -387.403)"/>
  <path id="Path_400" data-name="Path 400" class="cls-5" d="M708.494,571.583q1.131,1.736,2.121,3.557a13.079,13.079,0,0,1,1.121,2.535c.41,1.414.4,2.924.707,4.366.354,1.7,1.181,3.316,1.181,5.056a7.27,7.27,0,0,1-1.492,4.158,19.736,19.736,0,0,1-3.118,3.217,66.33,66.33,0,0,1-4.412-21.64,7.592,7.592,0,0,1,.332-3.04c.23-.6,1.333-2.248,2.043-1.6.258.237.325,1.3.484,1.662A12.779,12.779,0,0,0,708.494,571.583Z" transform="translate(-486.832 -387.403)"/>
  <path id="Path_401" data-name="Path 401" class="cls-6" d="M388.244,423.472c-2.8,2.793-3.132,4.023-4.812,4.578s-7.721,3.684-5.823,7.152l5.105,3.373.148.1.781,2.347-7.721,13.745s-12.727-5.819-13.975,2.789-.456,10.96-.792,15.555.11,5.925-1.011,5.925-1.563-3.019-1.563-3.019-.071-.138-.194-.4c-1.665-3.461-12.968-28.87,5.137-46.334,0,0,8.386-.552,10.4-10.168,1.393-6.646,6.265-6.074,9.5-4.72a10.741,10.741,0,0,1,2.906,1.708,14.312,14.312,0,0,1,2.082,2.475C389.344,420.039,389.846,421.867,388.244,423.472Z" transform="translate(-259.788 -287.524)"/>
  <path id="Path_402" data-name="Path 402" class="cls-5" d="M390.592,484.481l.781,2.347-7.721,13.745s-12.727-5.819-13.975,2.789-.456,10.96-.792,15.555.11,5.925-1.011,5.925-1.563-3.019-1.563-3.019-.071-.138-.194-.4c-.562-4.3-3.412-29.446,6.919-28.109,0,0,8.382,3.917,9.722,2.355,1.061-1.237,5.77-8.354,7.686-11.274Z" transform="translate(-267.516 -333.332)"/>
  <path id="Path_403" data-name="Path 403" class="cls-5" d="M424.347,420.1l-5.105,5.8s-5.929,7.933-7.159,7.6a5.738,5.738,0,0,0-1.121-.131,2.178,2.178,0,0,1-1.414-3.67c2.022-2.121,5.748-6.148,5.992-7.042a61.753,61.753,0,0,1,3.8-6.735,10.74,10.74,0,0,1,2.906,1.708A14.315,14.315,0,0,1,424.347,420.1Z" transform="translate(-295.713 -289.062)"/>
  <path id="Path_404" data-name="Path 404" class="cls-16" d="M573.68,551.662c-.866,1.732-5.3,8.23-7.244,11.069l-.909,1.336s-.354,2.347-6.148-.562S417.443,503.15,417.443,503.15l-.923-2.772,6.717-10.058,149.216,57.651S574.8,549.424,573.68,551.662Z" transform="translate(-300.607 -337.159)"/>
  <path id="Path_405" data-name="Path 405" class="cls-5" d="M566.436,581.123l-.909,1.336s-.354,2.347-6.148-.562-141.937-60.355-141.937-60.355l-.923-2.772Z" transform="translate(-300.607 -355.551)"/>
  <path id="Path_406" data-name="Path 406" class="cls-17" d="M598.879,588.815a4.96,4.96,0,0,0-3.991.781,3.284,3.284,0,0,0-1.145,2.178c-.092.979.477,2.142,1.46,2.217a4.66,4.66,0,0,0,.707-.046,10.562,10.562,0,0,1,1.852.156,7.562,7.562,0,0,0,6.445-2.6c.845-1.061-.269-1.52-1.266-1.732C601.577,589.476,600.244,589.034,598.879,588.815Z" transform="translate(-415.169 -400.767)"/>
  <path id="Path_407" data-name="Path 407" class="cls-3" d="M672.7,662.941l.877,6.229a9.489,9.489,0,0,1,.134,1.948,13.723,13.723,0,0,1-1.446,4.14c-.562,1.358-.905,2.984-.159,4.242.23.392.552.728.76,1.135a4.568,4.568,0,0,1,.354,1.99l.24,8.838a55.63,55.63,0,0,0,8.195-.046.456.456,0,0,0,.3-.092.481.481,0,0,0,.1-.322c.12-2.068-.24-4.14-.124-6.208.042-.757.148-1.51.163-2.27.042-3.079-1.478-6.095-1.018-9.139a20.141,20.141,0,0,1,.566-2.273c1.453-5.3,1.145-10.875.824-16.351a.385.385,0,0,0-.071-.247.375.375,0,0,0-.191-.1,10.058,10.058,0,0,0-9.114,1.732c-.385.311-1.358.612-1.612.983-.354.516.11,1.032.315,1.58a20.113,20.113,0,0,1,.909,4.232Z" transform="translate(-465.343 -442.966)"/>
  <path id="Path_408" data-name="Path 408" class="cls-5" d="M672.7,662.941l.877,6.229a9.489,9.489,0,0,1,.134,1.948,13.723,13.723,0,0,1-1.446,4.14c-.562,1.358-.905,2.984-.159,4.242.23.392.552.728.76,1.135a4.568,4.568,0,0,1,.354,1.99l.24,8.838a55.63,55.63,0,0,0,8.195-.046.456.456,0,0,0,.3-.092.481.481,0,0,0,.1-.322c.12-2.068-.24-4.14-.124-6.208.042-.757.148-1.51.163-2.27.042-3.079-1.478-6.095-1.018-9.139a20.141,20.141,0,0,1,.566-2.273c1.453-5.3,1.145-10.875.824-16.351a.385.385,0,0,0-.071-.247.375.375,0,0,0-.191-.1,10.058,10.058,0,0,0-9.114,1.732c-.385.311-1.358.612-1.612.983-.354.516.11,1.032.315,1.58a20.113,20.113,0,0,1,.909,4.232Z" transform="translate(-465.343 -442.966)"/>
  <path id="Path_409" data-name="Path 409" class="cls-3" d="M697.5,657.116a15.332,15.332,0,0,0-1.216,2.581c-.643,2.436,1.022,4.949.824,7.445-.064.788-.308,1.556-.325,2.344a2.187,2.187,0,0,0,.972,2.029c.47.24,1.061.216,1.5.491a2.426,2.426,0,0,1,.757,2.362,3.46,3.46,0,0,0,.354,2.521c.421.527,1.142.732,1.594,1.23a6.069,6.069,0,0,1,.682,1.152c.675,1.181,1.909,1.9,2.92,2.828s1.867,2.284,1.414,3.567a3.934,3.934,0,0,1-2.164,1.962,22.953,22.953,0,0,1-5.151,1.821,1.244,1.244,0,0,1-.59.032,1.262,1.262,0,0,1-.516-.354,9.592,9.592,0,0,1-2.6-3.468,3.558,3.558,0,0,0-.4-.948c-.552-.774-1.715-.746-2.5-1.28-.919-.626-1.142-1.867-1.743-2.8a31.367,31.367,0,0,0-2.121-2.319c-1.082-1.372-1.347-3.2-2.121-4.769-.329-.665-.749-1.287-1.011-1.98a10.809,10.809,0,0,1-.484-2.56c-.5-4.129-1.768-8.287-.969-12.374.177-.9.449-1.768.562-2.694.194-1.6-.113-3.242.124-4.84a1.333,1.333,0,0,1,.534-1.029,1.413,1.413,0,0,1,.608-.124,41.456,41.456,0,0,1,6.286.141,3.9,3.9,0,0,1,2.885,1.184,6.325,6.325,0,0,1,.686,2.637,6.506,6.506,0,0,0,1.368,2.584C698.252,655.359,697.934,656.218,697.5,657.116Z" transform="translate(-473.751 -439.018)"/>
  <path id="Path_410" data-name="Path 410" class="cls-15" d="M660.624,752.574a2.48,2.48,0,0,1,1.609.318,2.124,2.124,0,0,1,.46,1.941,29.131,29.131,0,0,1-1.513,5.657l-1.881,5.519a12.96,12.96,0,0,0-.658,2.432,27.454,27.454,0,0,0,.134,4.242,3.325,3.325,0,0,1-.488,2.061c-.8,1.029-2.358.909-3.641.707l-6.261-1.061a7.444,7.444,0,0,1-1.549-.378,10.814,10.814,0,0,1-1.5-.845,23.131,23.131,0,0,0-3.889-1.768c-.587-.219-1.244-.5-1.457-1.089a1.526,1.526,0,0,1,1-1.768,5.418,5.418,0,0,1,2.217-.191,16.618,16.618,0,0,0,5.444-.735,3.443,3.443,0,0,0,1.435-.742,3.535,3.535,0,0,0,.827-1.665,24.808,24.808,0,0,0,.947-5.656c.074-1.414.025-2.828.081-4.242.035-.838-.046-3.535.689-4.112.562-.431,2.874.76,3.634.933A17.678,17.678,0,0,0,660.624,752.574Z" transform="translate(-445.032 -505.748)"/>
  <path id="Path_411" data-name="Path 411" class="cls-15" d="M730.9,755.381a60.5,60.5,0,0,1,1.1,6.25,10.169,10.169,0,0,0,2.641,5.656,3.491,3.491,0,0,1,.767.849,1.789,1.789,0,0,1-.265,1.849,4.641,4.641,0,0,1-1.552,1.191A36,36,0,0,1,720.579,775a5.483,5.483,0,0,1-2.411-.088,1.989,1.989,0,0,1-1.439-1.768.9.9,0,0,1,.212-.612,1.318,1.318,0,0,1,.477-.279,14.074,14.074,0,0,0,4.653-2.7,5.156,5.156,0,0,0,1.6-4.9,9.693,9.693,0,0,0-1.191-2.266,38.3,38.3,0,0,1-1.768-3.426l-1.556-3.263a8.559,8.559,0,0,1-.993-2.991,14.352,14.352,0,0,0,5.734-.919,9.02,9.02,0,0,0,2.358-1.414c.6-.5,1.149-1.439,1.959-.785.651.523,1.061,2,1.442,2.743A20.147,20.147,0,0,1,730.9,755.381Z" transform="translate(-494.682 -504.624)"/>
  <path id="Path_412" data-name="Path 412" class="cls-17" d="M679.219,532.581c.156,1.358,1.471,2.252,2.726,2.789L674,537.905a3.578,3.578,0,0,0-.41-3.182,18.007,18.007,0,0,0-1.414-2.266c-.078-.11-.166-.247-.11-.375a.4.4,0,0,1,.223-.173,19.973,19.973,0,0,1,5.1-1.746c.516-.074,1.492-.279,1.877.244S679.145,531.955,679.219,532.581Z" transform="translate(-465.798 -362.85)"/>
  <path id="Path_413" data-name="Path 413" class="cls-5" d="M679.219,532.581c.156,1.358,1.471,2.252,2.726,2.789L674,537.905a3.578,3.578,0,0,0-.41-3.182,18.007,18.007,0,0,0-1.414-2.266c-.078-.11-.166-.247-.11-.375a.4.4,0,0,1,.223-.173,19.973,19.973,0,0,1,5.1-1.746c.516-.074,1.492-.279,1.877.244S679.145,531.955,679.219,532.581Z" transform="translate(-465.798 -362.85)"/>
  <circle id="Ellipse_64" data-name="Ellipse 64" class="cls-17" cx="6.229" cy="6.229" r="6.229" transform="translate(202.575 158.372)"/>
  <path id="Path_414" data-name="Path 414" class="cls-15" d="M663.025,587.706a2.751,2.751,0,0,0,2.6.707c2.174-.354,3.953-1.955,6.081-2.531,2.609-.707,5.37.2,7.834,1.315s4.914,2.475,7.608,2.666c-2.436-5.43-1.043-11.964-3.373-17.44a13.976,13.976,0,0,1-1.492-4.08c-.159-1.8.739-3.535.877-5.324a12.955,12.955,0,0,0-.813-4.7l-1.7-5.6a21.441,21.441,0,0,0-2.524-6.01c-.456-.661-.594-2-1.287-2.4-1.513-.873-3.808.226-5.448.286a5.688,5.688,0,0,0-4.094,2.545,16.367,16.367,0,0,0-2.121,4.691,44.6,44.6,0,0,0-1.884,7.505c-.053.382-.1.764-.134,1.149-.354,3.631.163,7.283.191,10.931v.025c.035,4.628-.707,9.227-.845,13.851A3.7,3.7,0,0,0,663.025,587.706Z" transform="translate(-459.62 -371.842)"/>
  <path id="Path_415" data-name="Path 415" class="cls-5" d="M664.348,578.122a7.977,7.977,0,0,0,3.27-1.959,13.669,13.669,0,0,0,2.171-2.475,17.629,17.629,0,0,0,1.87-3.914,45.738,45.738,0,0,0,2.475-11.409,4.358,4.358,0,0,0-.513-3.04,2.56,2.56,0,0,0-3.093-.75,5.126,5.126,0,0,0-1.877,2.019,26.069,26.069,0,0,0-2.634,4.886c-.283.771-.491,1.563-.767,2.337a21.557,21.557,0,0,1-.969,2.238c-.053.382-.1.764-.134,1.149C663.818,570.836,664.32,574.474,664.348,578.122Z" transform="translate(-460.617 -378.546)"/>
  <path id="Path_416" data-name="Path 416" class="cls-15" d="M643.074,553.384a4.382,4.382,0,0,1,.516,3.037,45.667,45.667,0,0,1-2.475,11.409,17.475,17.475,0,0,1-1.87,3.945,13.815,13.815,0,0,1-2.171,2.475,6.773,6.773,0,0,1-4.3,2.089,8.188,8.188,0,0,1-2.673-.583l-13.275-4.518a5.374,5.374,0,0,1,.65-2.369l.707-2.1a2.666,2.666,0,0,1,.962-1.52c.527-.315,1.361.117,1.8.467s.771.81,1.2,1.167a6.79,6.79,0,0,0,2.934,1.167l2.98.668a12.442,12.442,0,0,0,3.316.442c.831-2.538,2.411-4.773,3.305-7.29.276-.771.484-1.566.771-2.337a25.975,25.975,0,0,1,2.634-4.886,5.122,5.122,0,0,1,1.877-2.019A2.556,2.556,0,0,1,643.074,553.384Z" transform="translate(-430.088 -377.285)"/>
  <path id="Path_417" data-name="Path 417" class="cls-15" d="M668.789,507.988a3.143,3.143,0,0,0-.375-1.167.979.979,0,0,0-1.061-.467c-.622.194-.831,1.156-1.481,1.188-.438.021-.753-.424-.891-.841a2.564,2.564,0,0,0-.541-1.188,2.015,2.015,0,0,0-.771-.354c-1.51-.477-3.069-1.358-3.571-2.86a1.712,1.712,0,0,1-.081-.958c.23-.951,1.414-1.237,2.316-1.58s1.768-.93,2.652-1.375a4.357,4.357,0,0,1,2.881-.534,15.018,15.018,0,0,1,1.453.541c1.464.509,3.256.272,4.391,1.329a4.536,4.536,0,0,1,1.061,1.853c.792,2.2,1.294,4.808-.018,6.738-.81,1.191-1.594,2.553-3.15,2.7C669.793,511.163,669.075,509.522,668.789,507.988Z" transform="translate(-457.998 -341.98)"/>
  <g id="Group_169" data-name="Group 169" class="cls-2" transform="translate(202 158.192)">
    <path id="Path_418" data-name="Path 418" d="M666.05,510.33a2.6,2.6,0,0,0-.537-1.188,2.015,2.015,0,0,0-.771-.354c-1.513-.477-3.069-1.358-3.574-2.86a1.743,1.743,0,0,1-.078-.958,1.2,1.2,0,0,1,.187-.421,1.852,1.852,0,0,0-1.22,1.17,1.71,1.71,0,0,0,.081.958c.5,1.5,2.061,2.383,3.571,2.86a2.015,2.015,0,0,1,.771.354,2.585,2.585,0,0,1,.541,1.188c.141.421.453.863.891.841s.651-.421.93-.757A1.125,1.125,0,0,1,666.05,510.33Z" transform="translate(-660.023 -504.55)"/>
    <path id="Path_419" data-name="Path 419" d="M686.6,524.524c-1.817.166-2.535-1.488-2.828-3.012a3.075,3.075,0,0,0-.354-1.167.983.983,0,0,0-1.061-.467,1.881,1.881,0,0,0-.781.707,1.06,1.06,0,0,1,.8.495,3.143,3.143,0,0,1,.375,1.167c.286,1.524,1,3.182,2.828,3.012a2.916,2.916,0,0,0,1.867-.969A2.637,2.637,0,0,1,686.6,524.524Z" transform="translate(-673.959 -514.444)"/>
  </g>
  <ellipse id="Ellipse_65" data-name="Ellipse 65" class="cls-8" cx="11.416" cy="2.195" rx="11.416" ry="2.195" transform="translate(276.046 229.645)"/>
  <ellipse id="Ellipse_66" data-name="Ellipse 66" class="cls-3" cx="1.329" cy="1.739" rx="1.329" ry="1.739" transform="translate(285.941 229.167)"/>
  <ellipse id="Ellipse_67" data-name="Ellipse 67" class="cls-3" cx="1.329" cy="1.739" rx="1.329" ry="1.739" transform="translate(285.941 227.039)"/>
  <ellipse id="Ellipse_68" data-name="Ellipse 68" class="cls-3" cx="1.329" cy="1.739" rx="1.329" ry="1.739" transform="translate(285.941 224.914)"/>
  <ellipse id="Ellipse_69" data-name="Ellipse 69" class="cls-3" cx="1.329" cy="1.739" rx="1.329" ry="1.739" transform="translate(285.941 222.786)"/>
  <ellipse id="Ellipse_70" data-name="Ellipse 70" class="cls-3" cx="1.329" cy="1.739" rx="1.329" ry="1.739" transform="translate(285.941 220.658)"/>
  <ellipse id="Ellipse_71" data-name="Ellipse 71" class="cls-3" cx="1.329" cy="1.739" rx="1.329" ry="1.739" transform="translate(285.941 218.533)"/>
  <ellipse id="Ellipse_72" data-name="Ellipse 72" class="cls-3" cx="1.329" cy="1.739" rx="1.329" ry="1.739" transform="translate(285.941 216.405)"/>
  <path id="Path_420" data-name="Path 420" class="cls-8" d="M894.51,614.176a6.43,6.43,0,0,0,.495-.728l-3.493-.573,3.779.025a6.4,6.4,0,0,0,.12-5.045l-5.07,2.63,4.674-3.436a6.364,6.364,0,1,0-10.507,7.127,6.335,6.335,0,0,0-.728,1.16l4.536,2.358-4.836-1.623a6.364,6.364,0,0,0,1.029,5.968,6.364,6.364,0,1,0,10.009,0,6.364,6.364,0,0,0,0-7.873Z" transform="translate(-602.266 -410.572)"/>
  <path id="Path_421" data-name="Path 421" class="cls-5" d="M883.12,643.929a6.331,6.331,0,0,0,1.372,3.938,6.364,6.364,0,1,0,10.009,0C895.345,646.782,883.12,643.214,883.12,643.929Z" transform="translate(-602.248 -436.401)"/>
  <ellipse id="Ellipse_73" data-name="Ellipse 73" class="cls-8" cx="16.436" cy="3.161" rx="16.436" ry="3.161" transform="translate(36.036 271.489)"/>
  <ellipse id="Ellipse_74" data-name="Ellipse 74" class="cls-3" cx="1.913" cy="2.507" rx="1.913" ry="2.507" transform="translate(50.283 270.799)"/>
  <ellipse id="Ellipse_75" data-name="Ellipse 75" class="cls-3" cx="1.913" cy="2.507" rx="1.913" ry="2.507" transform="translate(50.283 267.738)"/>
  <ellipse id="Ellipse_76" data-name="Ellipse 76" class="cls-3" cx="1.913" cy="2.507" rx="1.913" ry="2.507" transform="translate(50.283 264.676)"/>
  <ellipse id="Ellipse_77" data-name="Ellipse 77" class="cls-3" cx="1.913" cy="2.507" rx="1.913" ry="2.507" transform="translate(50.283 261.615)"/>
  <ellipse id="Ellipse_78" data-name="Ellipse 78" class="cls-3" cx="1.913" cy="2.507" rx="1.913" ry="2.507" transform="translate(50.283 258.553)"/>
  <ellipse id="Ellipse_79" data-name="Ellipse 79" class="cls-3" cx="1.913" cy="2.507" rx="1.913" ry="2.507" transform="translate(50.283 255.488)"/>
  <ellipse id="Ellipse_80" data-name="Ellipse 80" class="cls-3" cx="1.913" cy="2.507" rx="1.913" ry="2.507" transform="translate(50.283 252.426)"/>
  <path id="Path_422" data-name="Path 422" class="cls-8" d="M226.569,691.771a9.626,9.626,0,0,0,.707-1.061l-5.027-.827,5.437.042a9.192,9.192,0,0,0,.173-7.265l-7.29,3.8,6.717-4.949a9.167,9.167,0,1,0-15.142,10.252,9.13,9.13,0,0,0-1.061,1.669l6.53,3.394-6.961-2.337a9.167,9.167,0,0,0,1.506,8.623,9.167,9.167,0,1,0,14.41,0,9.164,9.164,0,0,0,0-11.334Z" transform="translate(-167.211 -457.788)"/>
  <path id="Path_423" data-name="Path 423" class="cls-5" d="M210.238,734.747A9.138,9.138,0,0,0,212.2,740.4a9.167,9.167,0,1,0,14.411,0C227.83,738.841,210.238,733.7,210.238,734.747Z" transform="translate(-167.252 -495.083)"/>
</svg>
