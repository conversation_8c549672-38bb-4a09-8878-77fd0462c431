FROM node:10-alpine as builder

RUN mkdir /app
WORKDIR /app
COPY package.json /app
RUN npm install --legacy-peer-deps

ARG ENV=production
COPY . /app
RUN REACT_APP_DEP_TYPE=$ENV npm run build

FROM nginx:1.17.1-alpine
COPY nginx/default.conf /etc/nginx/conf.d/
## Remove default nginx website
RUN rm -rf /usr/share/nginx/html/*
## From 'builder' stage copy over the artifacts in dist folder to default nginx public folder
COPY --from=builder /app/build /usr/share/nginx/html
CMD ["nginx", "-g", "daemon off;"]