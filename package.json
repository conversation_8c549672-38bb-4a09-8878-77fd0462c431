{"name": "precium", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "^1.3.13", "@date-io/dayjs": "^1.3.13", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.10.4", "@emotion/styled": "^11.10.4", "@hookform/resolvers": "^5.0.1", "@mui/icons-material": "^5.6.2", "@mui/lab": "^5.0.0-alpha.88", "@mui/material": "^5.10.5", "@mui/system": "^5.10.5", "@mui/x-date-pickers": "^5.0.12", "@react-google-maps/api": "^2.19.3", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "abortcontroller-polyfill": "^1.5.0", "chart.js": "^2.9.3", "chartjs-plugin-datalabels": "^0.5.0", "date-fns": "^2.14.0", "dayjs": "^1.11.5", "hex-color-to-color-name": "^1.0.2", "husky": "^4.3.0", "lint-staged": "^10.4.2", "markdown-to-jsx": "^7.5.0", "material-ui-popup-state": "^1.6.1", "moment": "^2.27.0", "react": "^18.2.0", "react-chartjs-2": "^2.1.0", "react-charts": "^2.0.0-beta.7", "react-color": "^2.19.3", "react-color-palette": "^7.2.2", "react-dom": "^18.2.0", "react-draggable": "^4.4.5", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.1", "react-modern-calendar-datepicker": "RemoteState/react-modern-calendar-datepicker#master", "react-quill": "^2.0.0", "react-rating-stars-component": "^2.2.0", "react-router-dom": "^5.2.0", "react-scripts": "^3.4.3", "react-star-rating-component": "^1.4.1", "react-table": "^7.8.0", "react-virtualized": "^9.22.2", "recharts": "^2.1.9", "save-dev": "0.0.1-security", "tippy.js": "^6.2.5", "use-debounce": "^7.0.1", "use-deep-compare-effect": "^1.3.1", "yup": "^1.6.1"}, "devDependencies": {"firebase-tools": "^8.7.0", "prettier": "2.1.2"}, "scripts": {"start": "export SET NODE_OPTIONS=--openssl-legacy-provider && REACT_APP_DEP_TYPE=development react-scripts start", "build": "export SET NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "deploy-dev": "REACT_APP_DEP_TYPE=development npm run build && firebase deploy --only hosting:precium-dev", "deploy-prod": "REACT_APP_DEP_TYPE=production npm run build && firebase deploy --only hosting:precium-ui", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\""}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"**/*": "prettier --write --ignore-unknown"}}