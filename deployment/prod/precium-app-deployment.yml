apiVersion: apps/v1
kind: Deployment
metadata:
  name: precium-app
  namespace: precium-app-prod
  labels:
    app: precium-app
spec:
  replicas: 2
  selector:
    matchLabels:
      app: precium-app
  template:
    metadata:
      labels:
        app: precium-app
    spec:
      containers:
        - name: precium-app
          image: remotestate/react-precium:prod-latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
