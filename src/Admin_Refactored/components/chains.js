import { Box, Breadcrumbs, TextField as TF, Typography } from "@mui/material";
import { styled } from "@mui/system";
import { AddOutlined } from "@mui/icons-material";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import Autocomplete from "@mui/material/Autocomplete";
import React, { useEffect, useState } from "react";
import { useHistory, useParams } from "react-router-dom";
import { LoadingPage, useAuth } from "../../sdk";
import {
  ButtonContainer,
  Grid,
  HotelSection,
  Link,
  Main,
  OrganizationContainer,
  OrganizationSection,
  Page,
  RightSection,
} from "./Styles";

const TextField = styled(TF)`
  input {
    font: normal normal normal 16px/20px Roboto;
    letter-spacing: 0px;
    color: #000000;
  }
`;

const ClassWrapper = styled(Box)(({ theme }) => ({
  ".dropdownStyle": {
    borderRadius: 4,
    font: "normal normal normal 16px/20px Roboto",
  },
}));

const GridNew = styled(Grid)`
  padding-right: 24px;
`;
const OrganizationSectionNew = styled(OrganizationSection)`
  background: #ffffff;
  box-shadow: 0px 4px 10px rgba(3, 4, 94, 0.1);
  border-radius: 24px;
  padding-top: 24px;
  padding-bottom: 24px;
`;

const OrganizationContainerNew = styled(OrganizationContainer)`
  box-sizing: border-box;
  border-radius: 8px;
  height: 48px;
  border: 1px solid #0000004d;
  // margin-top: 24px;
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 20px;
  /* identical to box height, or 100% */
  padding-left: 20px;
  letter-spacing: 0.46px;

  color: #000000;

  &:hover {
    background: #2f6fbc;
    color: #ffffff;
    &.MuiSvgIcon-root {
      color: #fffff !important;
    }
  }
`;
const ArrowForwardIosIconNew = styled(ArrowForwardIosIcon)`
  height: 16px;
`;
const AddHotel = styled(Typography)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  /* identical to box height, or 171% */

  letter-spacing: 0.46px;
  text-transform: capitalize;

  /* Primary/Contrast */
  color: #ffffff;
`;

const TextFieldNew = styled(TextField)`
  background: #ffffff;
  box-shadow: 1px 2px 4px 1px rgba(48, 111, 188, 0.2);
  border-radius: 8px;
  padding-left: 8px;
  height: 40px;
  padding-top: 4px;
`;

export const Chains = ({ setSelectedChainName, selectedOrganizationName }) => {
  const { token, authFetch, permission } = useAuth();
  const history = useHistory();
  const [Loading, setLoading] = useState();
  const { ORGID, CHAINID } = useParams();
  const [chains, setChains] = useState([]);
  const [
    selectedManagingOrganization,
    setSelectedManagingOrganization,
  ] = useState(() => {
    if (ORGID) {
      return ORGID;
    } else {
      return 0;
    }
  });

  let cloneHotel = false;

  for (let key in permission) {
    if (permission.hasOwnProperty(key)) {
      if (permission[key].name === "cloneHotel") {
        cloneHotel = true;
      }
    }
  }

  let listOfOrg = null,
    searchHotel = null,
    addHotel = null;
  for (let key in permission) {
    if (permission.hasOwnProperty(key)) {
      if (permission[key].name === "listOfOrg") {
        listOfOrg = permission[key];
      }
      if (permission[key].name === "searchHotel") {
        searchHotel = permission[key];
      }
      if (permission[key].name === "addHotel") {
        addHotel = permission[key];
      }
    }
  }

  const defaultChainProps = {
    options: [
      ...chains
        .sort((a, b) =>
          String(a.name).toLowerCase() > String(b.name).toLowerCase() ? 1 : -1
        )
        .map((chain) => {
          return chain.name;
        }),
    ],
    getOptionLabel: (option) => option,
  };

  const handleChainChange = (value) => {
    const chainId = chains.find((chain) => chain.name === value);
    if (chainId) {
      setSelectedChainName(chainId.name);
      history.push(
        `/managing_organization/${selectedManagingOrganization}/chain/${chainId.id}/choose_hotel`
      );
    }
  };

  async function getChains() {
    setLoading(true);

    const { get } = await authFetch({
      path: `/managing-org/${selectedManagingOrganization}/chains`,
    });
    const { data, error } = await get();
    if (data) {
      setChains(data);
    } else {
      console.log(error);
    }
    setLoading(false);
  }

  useEffect(() => {
    if (!token) {
      return;
    } else if (listOfOrg) {
      getChains();
    }
  }, []);

  return (
    <ClassWrapper>
      <Page>
        <HotelSection>
          <Main style={{ height: "10%" }}>
            <Breadcrumbs
              separator="›"
              aria-label="breadcrumb"
              style={{ marginLeft: "20px" }}
            >
              <Link
                style={{ fontFamily: "Roboto" }}
                color="inherit"
                to="/managing_organization"
              >
                Home
              </Link>
              <Link
                style={{ fontFamily: "Roboto" }}
                color="inherit"
                to={
                  "/managing_organization/" +
                  selectedManagingOrganization +
                  "/chain"
                }
              >
                {selectedOrganizationName}
              </Link>
            </Breadcrumbs>
            <RightSection>
              <Autocomplete
                {...defaultChainProps}
                style={{
                  width: addHotel ? "50%" : "100%",
                  font: "Roboto",
                  maxWidth: "350px",
                }}
                freeSolo={true}
                onChange={(event, newValue) => {
                  handleChainChange(newValue);
                }}
                className="dropdownStyle"
                renderInput={(params) => (
                  <TextFieldNew
                    variant="standard"
                    {...params}
                    InputProps={{
                      ...params.InputProps,
                      disableUnderline: true,
                    }}
                    placeholder="Search Chain"
                  />
                )}
              />

              {addHotel && (
                <ButtonContainer
                  style={{
                    background: "#2F6FBC",
                    boxShadow:
                      "0px 3px 1px -2px rgba(48, 111, 188, 0.2), 0px 2px 2px rgba(48, 111, 188, 0.2), 0px 1px 5px rgba(48, 111, 188, 0.2)",
                    borderRadius: "8px",
                    width: "150px",
                  }}
                >
                  <Link to="/hotel">
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <AddOutlined style={{ width: "20px", color: "white" }} />
                      <AddHotel> Add Hotel</AddHotel>
                    </div>
                  </Link>
                </ButtonContainer>
              )}
            </RightSection>
          </Main>
          {!Loading ? (
            <OrganizationSectionNew>
              <GridNew>
                <OrganizationContainerNew
                  onClick={() => {
                    setSelectedChainName("All");
                  }}
                  to={`/managing_organization/${selectedManagingOrganization}/chain/all/choose_hotel`}
                >
                  All
                  <ArrowForwardIosIconNew />
                </OrganizationContainerNew>
                {chains
                  .sort((a, b) =>
                    String(a.name).toLowerCase() > String(b.name).toLowerCase()
                      ? 1
                      : -1
                  )
                  .map((chain, index) => (
                    <OrganizationContainerNew
                      key={index}
                      onClick={() => {
                        setSelectedChainName(chain.name);
                      }}
                      to={`/managing_organization/${selectedManagingOrganization}/chain/${chain.id}/choose_hotel`}
                    >
                      {chain.name}
                      <ArrowForwardIosIconNew />
                    </OrganizationContainerNew>
                  ))}

                <OrganizationContainerNew
                  onClick={() => {
                    setSelectedChainName("Standalone");
                  }}
                  to={`/managing_organization/${selectedManagingOrganization}/chain/${-1}/choose_hotel`}
                >
                  Standalone
                  <ArrowForwardIosIconNew />
                </OrganizationContainerNew>
              </GridNew>
            </OrganizationSectionNew>
          ) : (
            <LoadingPage />
          )}
        </HotelSection>
      </Page>
    </ClassWrapper>
  );
};
