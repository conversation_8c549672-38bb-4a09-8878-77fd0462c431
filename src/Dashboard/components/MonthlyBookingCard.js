import { Box, Tooltip, LinearProgress } from "@mui/material";
import MenuItem from "@mui/material/MenuItem";
import Select from "@mui/material/Select";
import React from "react";
import { styled } from "@mui/system";
import { formatNumber } from "../../sdk";
import {
  ARR,
  Body,
  Bookings,
  Card,
  CardName,
  Details,
  Header,
  Info,
  Revenue,
  RevPAR,
  Title,
  Value,
} from "./Styles";

const TextOnlyTooltipSegment = styled(Tooltip)(({ theme }) => ({
  ".tooltip": {
    fontFamily: "Roboto",
    fontStyle: "normal",
    fontWeight: "normal",
    fontSize: "12px",
    lineHeight: "12px",
    marginTop: "0px",
    marginLeft: "-60px",
    background: "#1E90FF",
    color: "#fffff",
  },
  ".arrow": {
    color: "#1E90FF",
  },
}));

const CardNameNew = styled(CardName)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 20px;

  text-transform: capitalize;

  color: #303030;
`;
const TitleNew = styled(Title)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 14px;
  color: #000000;
`;

const YEARBAR = styled(Box)`
  height: 14px;
  width: 4px;
  margin: auto;
  background: rgba(48, 111, 188, 0.3);
  transform: rotate(-90deg);
`;
const CURRENTYEARBAR = styled(Box)`
  height: 14px;
  width: 4px;
  margin: auto;
  background: #306fbc;
  transform: rotate(-90deg);
`;
const YEAR = styled(Box)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 14px;

  text-transform: capitalize;

  color: #000000;
`;
const Container = styled(Box)`
  display: "flex";
  flexdirection: "column";
`;
const IncreaseData = styled(Box)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 14px;

  text-transform: capitalize;

  color: #42ae40;
  padding-left: 8px;
`;

const DecreaseData = styled(Box)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 14px;

  text-transform: capitalize;

  color: #e13a3a;
  padding-left: 8px;
`;

const BorderLinearProgress = styled(LinearProgress)(({ theme }) => ({
  height: 7,

  "& .MuiLinearProgress-colorPrimary": {
    backgroundColor: "red",
  },
  "& .MuiLinearProgress-barColorPrimary": {
    background: "#306FBC",

    borderRadius: "0px 3px 3px 0px",
    "&:hover": {
      background: "#0d54ab",

      transition: "opacity .5s ease-in",
    },
  },

  width: "180px",
  backgroundColor: "white",
}));
const BorderLinearProgressNew = styled(LinearProgress)(({ theme }) => ({
  height: 7,

  "& .MuiLinearProgress-bar": {
    background: "rgba(48, 111, 188, 0.3)",
    borderRadius: "0px 3px 3px 0px",
    "&:hover": {
      background: "rgba(48, 111, 188, 0.5)",

      transition: "opacity .5s ease-in",
    },
  },
  width: "180px",
  backgroundColor: "white",
}));

export default function MonthlyBookingCard({
  data,
  selectedMonthYear,
  setSelectedMonthYear,
  yearOptions,
}) {
  const normaliseRS = () => {
    let diff =
      parseInt(data?.currentMonth?.booking) -
      parseInt(data?.previousYearMonth?.booking);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.previousYearMonth?.booking) -
        parseInt(data?.currentMonth?.booking);
      let result = (a / data?.previousYearMonth?.booking) * 100;
      return (100 - result).toFixed(2);
    }
  };
  const normaliseRS1 = () => {
    let diff =
      parseInt(data?.previousYearMonth?.booking) -
      parseInt(data?.currentMonth?.booking);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.currentMonth?.booking) -
        parseInt(data?.previousYearMonth?.booking);
      let result = (a / data?.currentMonth?.booking) * 100;
      return (100 - result).toFixed(2);
    }
  };

  const normaliseARR = () => {
    let diff =
      parseInt(data?.currentMonth?.arr) -
      parseInt(data?.previousYearMonth?.arr);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.previousYearMonth?.arr) -
        parseInt(data?.currentMonth?.arr);
      let result = (a / data?.previousYearMonth?.arr) * 100;
      return (100 - result).toFixed(2);
    }
  };

  const normaliseARR1 = () => {
    let diff =
      parseInt(data?.previousYearMonth?.arr) -
      parseInt(data?.currentMonth?.arr);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.currentMonth?.arr) -
        parseInt(data?.previousYearMonth?.arr);
      let result = (a / data?.currentMonth?.arr) * 100;
      return (100 - result).toFixed(2);
    }
  };
  const normaliseRevenue = () => {
    let diff =
      parseInt(data?.currentMonth?.revenue) -
      parseInt(data?.previousYearMonth?.revenue);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.previousYearMonth?.revenue) -
        parseInt(data?.currentMonth?.revenue);
      let result = (a / data?.previousYearMonth?.revenue) * 100;
      return (100 - result).toFixed(2);
    }
  };

  const normaliseRevenue1 = () => {
    let diff =
      parseInt(data?.previousYearMonth?.revenue) -
      parseInt(data?.currentMonth?.revenue);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.currentMonth?.revenue) -
        parseInt(data?.previousYearMonth?.revenue);
      let result = (a / data?.currentMonth?.revenue) * 100;
      return (100 - result).toFixed(2);
    }
  };

  const normaliseRevPAR1 = () => {
    let diff =
      parseInt(data?.previousYearMonth?.revPAR) -
      parseInt(data?.currentMonth?.revPAR);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.currentMonth?.revPAR) -
        parseInt(data?.previousYearMonth?.revPAR);
      let result = (a / data?.currentMonth?.revPAR) * 100;
      return (100 - result).toFixed(2);
    }
  };
  const normaliseRevPAR = () => {
    let diff =
      parseInt(data?.currentMonth?.revPAR) -
      parseInt(data?.previousYearMonth?.revPAR);
    if (diff > 0) return 100;
    else {
      let a =
        parseInt(data?.previousYearMonth?.revPAR) -
        parseInt(data?.currentMonth?.revPAR);
      let result = (a / data?.previousYearMonth?.revPAR) * 100;
      return (100 - result).toFixed(2);
    }
  };

  return (
    <Card>
      <Header>
        <CardNameNew
          style={{
            textAlign: "center",
            marginLeft: "auto",
            marginRight: "auto",
          }}
        >
          Month to Date Vs{" "}
          {parseInt(selectedMonthYear) === new Date().getFullYear() - 1
            ? "Last Year"
            : selectedMonthYear}
        </CardNameNew>
      </Header>
      <Body>
        <Info>
          <Details style={{ paddingTop: "15px" }}>
            <Bookings>
              <TitleNew>Rooms Sold</TitleNew>
              <Container>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(parseInt(data?.currentMonth?.booking))}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgress
                      variant="determinate"
                      value={normaliseRS()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(
                      parseInt(data?.previousYearMonth?.booking)
                    )}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgressNew
                      variant="determinate"
                      value={normaliseRS1()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
              </Container>
              <Box style={{ display: "flex", paddingLeft: "12px" }}>
                {parseInt(data?.currentMonth?.booking) -
                  parseInt(data?.previousYearMonth?.booking) >=
                0 ? (
                  <>
                    <img src="/assets/inc-icon.svg" alt="Increase Icon" />
                    <IncreaseData>
                      {formatNumber(
                        parseInt(data?.currentMonth?.booking) -
                          parseInt(data?.previousYearMonth?.booking)
                      )}
                    </IncreaseData>
                  </>
                ) : (
                  <>
                    <img src="/assets/dec-icon.svg" alt="Decrease Icon" />
                    <DecreaseData>
                      {formatNumber(
                        parseInt(data?.previousYearMonth?.booking) -
                          parseInt(data?.currentMonth?.booking)
                      )}
                    </DecreaseData>
                  </>
                )}
                {}
              </Box>
            </Bookings>

            <ARR>
              <TitleNew>ADR</TitleNew>
              <Container>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(
                      parseFloat(data?.currentMonth?.arr).toFixed(2)
                    )}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgress
                      variant="determinate"
                      value={normaliseARR()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(
                      parseFloat(data?.previousYearMonth?.arr).toFixed(2)
                    )}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgressNew
                      variant="determinate"
                      value={normaliseARR1()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
              </Container>
              <Box style={{ display: "flex", paddingLeft: "12px" }}>
                {parseFloat(data?.currentMonth?.arr).toFixed(2) -
                  parseFloat(data?.previousYearMonth?.arr).toFixed(2) >=
                0 ? (
                  <>
                    <img src="/assets/inc-icon.svg" alt="Increase Icon" />
                    <IncreaseData>
                      {formatNumber(
                        (
                          parseFloat(data?.currentMonth?.arr).toFixed(2) -
                          parseFloat(data?.previousYearMonth?.arr).toFixed(2)
                        ).toFixed(2)
                      )}
                    </IncreaseData>
                  </>
                ) : (
                  <>
                    <img src="/assets/dec-icon.svg" alt="Decrease Icon" />
                    <DecreaseData>
                      {formatNumber(
                        (
                          parseInt(data?.previousYearMonth?.arr).toFixed(2) -
                          parseInt(data?.currentMonth?.arr).toFixed(2)
                        ).toFixed(2)
                      )}
                    </DecreaseData>
                  </>
                )}
                {}
              </Box>
            </ARR>
            <Revenue>
              <TitleNew>Revenue</TitleNew>
              <Container>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(parseInt(data?.currentMonth?.revenue))}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgress
                      variant="determinate"
                      value={normaliseRevenue()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(
                      parseInt(data?.previousYearMonth?.revenue)
                    )}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgressNew
                      variant="determinate"
                      value={normaliseRevenue1()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
              </Container>
              <Box style={{ display: "flex", paddingLeft: "12px" }}>
                {parseInt(data?.currentMonth?.revenue) -
                  parseInt(data?.previousYearMonth?.revenue) >=
                0 ? (
                  <>
                    <img src="/assets/inc-icon.svg" alt="Increase Icon" />
                    <IncreaseData>
                      {formatNumber(
                        parseInt(data?.currentMonth?.revenue) -
                          parseInt(data?.previousYearMonth?.revenue)
                      )}
                    </IncreaseData>
                  </>
                ) : (
                  <>
                    <img src="/assets/dec-icon.svg" alt="Decrease Icon" />
                    <DecreaseData>
                      {formatNumber(
                        parseInt(data?.previousYearMonth?.revenue) -
                          parseInt(data?.currentMonth?.revenue)
                      )}
                    </DecreaseData>
                  </>
                )}
                {}
              </Box>
            </Revenue>
            <RevPAR>
              <TitleNew>RevPAR</TitleNew>
              <Container>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(
                      parseFloat(data?.currentMonth?.revPAR).toFixed(2)
                    )}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgress
                      variant="determinate"
                      value={normaliseRevPAR()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
                <Box>
                  <TextOnlyTooltipSegment
                    title={formatNumber(
                      parseFloat(data?.previousYearMonth?.revPAR).toFixed(2)
                    )}
                    placement="right"
                    arrow
                    style={{ cursor: "default" }}
                  >
                    <BorderLinearProgressNew
                      variant="determinate"
                      value={normaliseRevPAR1()}
                    />
                  </TextOnlyTooltipSegment>
                </Box>
              </Container>
              <Box style={{ display: "flex", paddingLeft: "12px" }}>
                {parseFloat(data?.currentMonth?.revPAR).toFixed(2) -
                  parseFloat(data?.previousYearMonth?.revPAR).toFixed(2) >=
                0 ? (
                  <>
                    <img src="/assets/inc-icon.svg" alt="Increase Icon" />
                    <IncreaseData>
                      {formatNumber(
                        (
                          parseFloat(data?.currentMonth?.revPAR).toFixed(2) -
                          parseFloat(data?.previousYearMonth?.revPAR).toFixed(2)
                        ).toFixed(2)
                      )}
                    </IncreaseData>
                  </>
                ) : (
                  <>
                    <img src="/assets/dec-icon.svg" alt="Decrease Icon" />
                    <DecreaseData>
                      {formatNumber(
                        (
                          parseFloat(data?.previousYearMonth?.revPAR).toFixed(
                            2
                          ) - parseFloat(data?.currentMonth?.revPAR).toFixed(2)
                        ).toFixed(2)
                      )}
                    </DecreaseData>
                  </>
                )}
                {}
              </Box>
            </RevPAR>
            <Box
              style={{
                display: "flex",
                justifyContent: "center",
                width: "50%",
                margin: "auto",
                paddingTop: "12px",
              }}
            >
              <YEARBAR />
              <Select
                value={selectedMonthYear}
                variant="standard"
                disableUnderline
                MenuProps={{
                  PaperProps: {
                    sx: {
                      maxHeight: 70,
                      scrollbarWidth: "none",
                    },
                  },
                }}
                onChange={(e) => setSelectedMonthYear(e.target.value)}
                sx={{
                  background: "transparent",
                  fontFamily: "Roboto",
                  fontStyle: "normal",
                  fontWeight: "500",
                  fontSize: "14px",
                  border: "none",
                  height: "14px",
                }}
              >
                {yearOptions.map((year) => (
                  <MenuItem
                    sx={{
                      background: "transparent",
                      fontFamily: "Roboto",
                      fontStyle: "normal",
                      fontWeight: "500",
                      fontSize: "14px",
                      border: "none",
                      height: "25px",
                    }}
                    value={year}
                  >
                    {year}
                  </MenuItem>
                ))}
              </Select>
              <CURRENTYEARBAR />
              <YEAR>{new Date().getFullYear()}</YEAR>
            </Box>
          </Details>
        </Info>
      </Body>
    </Card>
  );
}
