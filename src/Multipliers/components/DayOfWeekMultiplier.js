import {
  Checkbox,
  Snackbar,
  SnackbarContent,
  Table,
  TableBody,
} from "@mui/material";
import { Edit } from "@mui/icons-material";
import React from "react";
import { styled } from "@mui/system";
import { SecondaryButton, TableLoading } from "../../sdk";
import {
  CancelButton,
  CardContainer,
  Head,
  HeaderCard,
  SaveButton,
  Tabhead,
  TCell,
  TContainer,
  TextFieldSmall,
  THead,
  TRow,
} from "./../Styles";

const EditButton = styled(Edit)`
  height: 18px;
  width: 18px;
  color: #306fbc;
`;

export function DayOfWeekMultiplier({
  daysOfWeek,
  isInEditMode,
  setIsInEditMode,
  isCancelled,
  setIsCancelled,
  isSaved,
  setIsSaved,
  onChange,
  handleMultiplier,
  multipliers,
  open,
  handleCloseStatus,
  networkMsg,
  dowLoading,
  editDaysOfWeek,
  handleEditDaysOfWeekMultiplierChange,
}) {
  const Days = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
  ];

  return (
    <>
      <CardContainer>
        <HeaderCard>
          <Head>
            <div className="title">Day Of Week</div>
            <Checkbox
              checked={multipliers.isDayOfWeek}
              onChange={(e) =>
                handleMultiplier("isDayOfWeek", e.target.checked)
              }
              style={{
                color: "rgb(48,111,188)",
                width: "30px",
                height: "30px",
              }}
            />
          </Head>
          {!isInEditMode ? (
            <SecondaryButton style={{ border: "none" }}></SecondaryButton>
          ) : (
            <div>
              <CancelButton
                onClick={() => {
                  setIsCancelled(!isCancelled);
                  setIsInEditMode(!isInEditMode);
                }}
                style={{ margin: "5px 10px" }}
              >
                Cancel
              </CancelButton>
              <SaveButton
                onClick={() => {
                  setIsSaved(!isSaved);
                  setIsInEditMode(!isInEditMode);
                }}
                style={{ margin: "5px 10px" }}
              >
                Save
              </SaveButton>
            </div>
          )}
        </HeaderCard>

        {!dowLoading ? (
          <TContainer style={{ maxHeight: "50vh" }}>
            <Table
              stickyHeader
              aria-label="sticky table"
              style={{
                width: "100%",
                border: "1px solid rgba(0, 0, 0, 0.12)",

                borderRadius: "5px",
              }}
            >
              <THead>
                <TRow>
                  <Tabhead $isineditmode={isInEditMode}>Day</Tabhead>
                  <Tabhead $isineditmode={isInEditMode}>Value</Tabhead>
                  <Tabhead $isineditmode={isInEditMode}>Actions</Tabhead>
                </TRow>
              </THead>
              <TableBody>
                {daysOfWeek.map((row, idx) => (
                  <TRow key={idx}>
                    <TCell $isineditmode={isInEditMode}>{Days[row.day]}</TCell>
                    <TCell $isineditmode={isInEditMode}>
                      {editDaysOfWeek[idx] ? (
                        <TextFieldSmall
                          value={row.value}
                          type="number"
                          onChange={(e) =>
                            onChange(
                              idx,
                              "value",
                              e.target.value && parseFloat(e.target.value)
                            )
                          }
                        />
                      ) : !row.value ? (
                        0
                      ) : (
                        row.value
                      )}
                    </TCell>
                    <TCell>
                      <EditButton
                        onClick={() =>
                          handleEditDaysOfWeekMultiplierChange(idx)
                        }
                      />
                    </TCell>
                  </TRow>
                ))}
              </TableBody>
            </Table>
          </TContainer>
        ) : (
          <TableLoading />
        )}
      </CardContainer>
      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={open}
        autoHideDuration={3000}
        onClose={handleCloseStatus}
      >
        <SnackbarContent
          style={{ backgroundColor: "#CA3433" }}
          message={networkMsg}
        />
      </Snackbar>
    </>
  );
}
