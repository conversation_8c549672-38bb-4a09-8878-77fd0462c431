import { useEffect, useState } from "react";
import { useAuth, usePrevious } from "../../sdk";

const EMPTY_ROW = {
  min: 0,
  max: 100,
  mwValue: null,
  weValue: null,
};
export function useOccupancy(hotelId, copyFromHotelId, OC) {
  const [occupancies, setOccupancies] = useState([]);
  const [isInEditMode, setIsInEditMode] = useState(false);
  const saveButtonClicked = usePrevious(isInEditMode);

  const [open, setOpen] = useState(false);

  const handleCloseStatus = () => {
    setOpen(false);
  };

  const [networkMsg, setnetworkMsg] = useState(null);
  const { token, authFetch } = useAuth();

  function isValid(occupancy) {
    return (
      occupancy.min != null &&
      occupancy.max &&
      occupancy.mwValue &&
      occupancy.weValue &&
      occupancy.max >= occupancy.min
    );
  }

  useEffect(() => {
    if (!token) return;
    refreshOccupancies();
  }, [token, hotelId]);

  useEffect(() => {
    if (isInEditMode) {
      const allValidEntries = occupancies.every(isValid);
      const lastEntry = occupancies.length
        ? occupancies[occupancies.length - 1]
        : {
            max: -1,
          };
      const roomLeftForEntries = lastEntry.max < 100;
      if (allValidEntries && roomLeftForEntries) {
        setOccupancies([
          ...occupancies,
          {
            ...EMPTY_ROW,
            min: lastEntry + 1,
          },
        ]);
      }
      return;
    } else if (saveButtonClicked) {
      updateOccupancies();
    }
  }, [occupancies, isInEditMode]);

  useEffect(() => {
    if (copyFromHotelId === null && !OC) return;
    refreshOccupancies(copyFromHotelId);
  }, [OC]);

  async function refreshOccupancies(anotherHotelId = false) {
    const hotelIdToUse = anotherHotelId ? anotherHotelId : hotelId;
    const { get } = await authFetch({
      path: `/hotel/${hotelIdToUse}/occupancy-multiplier`,
    });
    const { data, error } = await get();
    if (error) {
      setnetworkMsg("Can't Fetch Occupancy");
      setOpen(true);
      console.log(error);
    }
    if (data) {
      setOccupancies(data);
    } else {
      setOccupancies([]);
    }

    if (anotherHotelId) {
      setIsInEditMode(true);
    }
  }

  const updateOccupancies = async () => {
    console.log(occupancies);
    const { post } = await authFetch({
      path: `/hotel/${hotelId}/occupancy-multiplier/all`,
    });
    const { error, response, data } = await post(occupancies.filter(isValid));
    if (!response?.ok) {
      setnetworkMsg(data?.messageToUser || "Can't Update Occupancy");
      setOpen(true);
      console.log(error);
    }
    refreshOccupancies();
  };

  function changeHandler(index, key, value) {
    if (value < 0) {
      value = value * -1;
    }

    setOccupancies((prevState) => {
      return prevState.map((row, idx) =>
        idx === index
          ? {
              ...row,
              [key]: value,
            }
          : row
      );
    });
  }

  return {
    occupancies,
    oIsInEditMode: isInEditMode,
    setOIsInEditMode: setIsInEditMode,
    oChangeHandler: changeHandler,
    oopen: open,
    ohandleCloseStatus: handleCloseStatus,
    onetworkMsg: networkMsg,
  };
}
