import {
  <PERSON>,
  Button,
  Input,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>H<PERSON>,
  TableRow,
  Typography,
} from "@mui/material";
import { styled } from "@mui/system";

export const CancelButton = styled(Button)`
  background: #ffffff;
  border: 1px solid #306fbc;
  box-sizing: border-box;
  box-shadow: 0px 4px 4px rgba(3, 4, 94, 0.2);
  border-radius: 8px;
  padding: 6px 12px 6px 12px;
  color: #306fbc;
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  text-transform: capitalize;

  height: 20px;
  &:hover {
    background: #ffffff;
  }
`;
export const SaveButton = styled(Button)`
  background: #757575;
  box-shadow: 0px 4px 4px rgba(3, 4, 94, 0.2);
  border-radius: 8px;
  padding: 6px 12px 6px 12px;
  color: #ffffff;
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 15px;
  text-transform: capitalize;

  height: 20px;
  &:hover {
    background: #757575;
  }
`;

export const PageContainer = styled(Box)`
  height: 100%;
  width: 100%;
  padding: 0px 20px;
  .MuiGridList-root {
    flex-wrap: nowrap;
  }
`;
export const Heading = styled(Box)`
  font: 500 24px/30px Roboto;
  letter-spacing: 0px;
  color: #0d0d0d;
`;

export const Container = styled(Box)`
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
  grid-gap: 10px;
`;
export const Tabhead = styled(TableCell)`
  &.MuiTableCell-root {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;

    color: #ffffff;
    background: #306fbc;
    letter-spacing: 0px;

    padding: 5px 8px;

    width: 100px;
    height: 56px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    text-align: ${(props) => (props.$isineditmode ? "center" : "center")};
    text-transform: capitalize;
    &:first-child {
      border-radius: 4px 0px 0px 0px;
    }
    &:last-child {
      border-radius: 0px 4px 0px 0px;
    }
  }
`;
export const CardContainer = styled(Box)`
  background: #ffffff 0% 0% no-repeat padding-box;
  padding: 16px;
  margin-right: 10px;
  .title {
    text-align: left;
    font: normal normal normal 16px/20px Roboto;
    letter-spacing: 0px;
    color: #130453;
    opacity: 1;
    padding: 0 20px;
  }
  button {
    font: 500 12px/15px Roboto;
    margin: 5px 16px;
  }
`;

export const TCell = styled(TableCell)`
  &.MuiTableCell-root {
    font-family: "Roboto";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;

    color: #000000;
    letter-spacing: 0px;

    padding: 5px 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    text-align: ${(props) => (props.isInEditMode ? "center" : "center")};
    text-transform: capitalize;
  }
`;

export const TRow = styled(TableRow)`
  &.MuiTableRow-root {
  }
`;
export const THead = styled(TableHead)`
  &.MuiTableHead-root {
    tr.MuiTableRow-root {
      padding: 5px 10px;
      background: transparent;
    }
  }
`;

export const TContainer = styled(TableContainer)`
  &.MuiPaper-elevation1 {
    box-shadow: none;
  }
`;

export const TextFieldSmall = styled(Input)`
  input.MuiInputBase-input {
    width: 40px;
    padding: 5px 0;
    font-family: 'Roboto';
font-style: normal;
font-weight: 400;
font-size: 14px;
line-height: 20px;



color: #000000;
    letter-spacing: 0px;
    color: #333333;
    text-align: center;
  }
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
`;

export const UploadTextBtn = styled(Typography)`
  text-align: right;
  font: Bold 16px/20px Roboto;
  letter-spacing: 0px;
  color: #343434;
  :hover {
    cursor: pointer;
  }
`;

export const Header = styled(Box)`
  width: 100%;
  padding: 0 22px 0 0;
  margin-bottom: 52px;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

export const Head = styled(Box)`
  display: flex;
  width: 50%;
  justify-content: space-between;
  align-items: center;
`;
export const ScrollSection = styled(Box)`
  width: 300px;
  height: 200px;
  overflow: scroll;
`;
export const HeaderCard = styled(Box)`
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
  .title {
    text-align: left;

    letter-spacing: 0px;

    opacity: 1;
    padding: 0 20px;
    font-family: "Roboto";
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;

    color: #000000;
  }
`;
