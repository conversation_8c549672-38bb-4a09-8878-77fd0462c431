import {
  Button,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>nackbar<PERSON>ontent,
  Stack,
  Typography,
  styled,
} from "@mui/material";
import React from "react";
import usePmsInterfaceSetup from "../hooks/usePmsInterfaceSetup";
import RoomMappingForm from "./RoomMappingForm";
import ConfirmationDialog from "./ConfirmationDialog";

const newRoom = {
  pmsRoomTypeId: null,

  pmsRateCodeId: null,

  pmsRateCodeType: null,
  discount: null,
  addToBasePrice: null,
  maxThresholdWeekday: null,
  minThresholdWeekday: null,
  maxThresholdWeekend: null,
  minThresholdWeekend: null,
  isNewRoom: true,
  isEdit: true,
};
export default function PmsInterfaceSetup() {
  const {
    hotelId,
    handleRedirect,
    snackbarMsg,
    setSnackBarMsg,
    isLoading,
    formData,
    setValue,
    register,
    handleSaveRooms,
    loaderRoomIndex,
    formState,
    watch,
    handleDeleteRoom,
    isDeleteConfirmation,
    setIsDeleteConfirmation,
  } = usePmsInterfaceSetup();

  return (
    <PmsInterfacePageStack>
      <Stack className="toggleContainer">
        <Typography className="pageHeading">PMS Interface Setup</Typography>
      </Stack>
      {formData.map((room, index) => (
        <RoomMappingForm
          key={room?.id ?? index}
          room={room}
          watch={watch}
          formState={formState}
          register={register}
          index={index}
          handleSaveRooms={handleSaveRooms}
          setValue={setValue}
          isLoading={isLoading}
          loaderRoomIndex={loaderRoomIndex}
          handleDeleteRoom={() => setIsDeleteConfirmation(room?.id)}
          errors={formState?.errors}
        />
      ))}

      <Stack
        className=""
        style={{
          marginLeft: "auto",
        }}
      >
        <Button
          variant="contained"
          onClick={() => {
            const currentRooms = formData || [];
            setValue("roomFields", [...currentRooms, newRoom]);
          }}
        >
          Add Room
        </Button>
      </Stack>
      <Stack className="buttonStack">
        <Stack direction="row" gap="16px" justifyContent="end">
          <Button
            variant="contained"
            onClick={() =>
              handleRedirect(`/hotel/${hotelId}/setup/market-segment`)
            }
          >
            Next
          </Button>
        </Stack>
      </Stack>
      {isDeleteConfirmation ? (
        <ConfirmationDialog
          open={!!isDeleteConfirmation}
          confirmationText={"Are you sure you want to delete this Room?"}
          handleCloseConfirmation={() => setIsDeleteConfirmation(null)}
          onSuccess={() => handleDeleteRoom(isDeleteConfirmation)}
        />
      ) : null}
      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={snackbarMsg?.open}
        autoHideDuration={6000}
        onClose={() => {
          setSnackBarMsg({
            ...snackbarMsg,
            open: false,
          });
        }}
      >
        <SnackbarContent
          style={{ backgroundColor: snackbarMsg?.color }}
          message={snackbarMsg?.msg}
        />
      </Snackbar>
    </PmsInterfacePageStack>
  );
}

const PmsInterfacePageStack = styled(Stack)(() => ({
  width: "100%",
  background: "#ffffff",
  border: "none",
  padding: "20px",
  outline: "none",
  height: "calc(100vh - 160px)",
  overflowY: "scroll",
  overflowX: "hidden",
  boxShadow: "1px 4px 10px rgba(48, 111, 188, 0.2)",
  gap: "24px",

  ".borderStack": {
    gap: "16px",
    padding: "24px",
    boxShadow: "1px 4px 10px rgba(48, 111, 188, 0.2)",
  },
  ".pageHeading": {
    fontSize: "16px",
    fontWeight: "600",
    paddingLeft: "16px",
  },
  ".toggleContainer": {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  ".propertyStack": {
    display: "flex",
    flexDirection: "row",
    gap: "16px",
    alignItems: "center",
  },
  ".buttonStack": {
    display: "flex",
    // flexDirection: "row",
    gap: "16px",
    justifyContent: "end",
  },
  ".remove-arrows": {
    "& input::-webkit-outer-spin-button, & input::-webkit-inner-spin-button": {
      display: "none",
    },
    "& input[type=number]": {
      MozAppearance: "textfield",
    },
    ".MuiInputAdornment-root": {
      "& fieldset": {
        border: "none",
      },
    },
  },
}));
