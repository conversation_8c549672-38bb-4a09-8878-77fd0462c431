import DeleteIcon from "@mui/icons-material/Delete";
import {
  Button,
  FormControl,
  FormHelperText,
  IconButton,
  InputLabel,
  MenuItem,
  OutlinedInput,
  Select,
  Snackbar,
  SnackbarContent,
  Stack,
  TextField,
  Typography,
  styled,
} from "@mui/material";
import LoadingButton from "@mui/lab/LoadingButton";
import React from "react";
import useInnkeyScheduling from "../hooks/useInnkeyScheduling";
import ConfirmationDialog from "./ConfirmationDialog";

export default function InnkeyScheduling() {
  const {
    formEdit,
    handleSaveScheduling,
    openSnackbar,
    setOpenSnackbar,
    snackBarMsg,
    handleAddScheduling,
    handleSchedulingChange,
    confirmationDialog,
    setConfirmationDialog,
    handleDeleteScheduling,
    schedulingItems,
    errors,
    scheduleLoading,
  } = useInnkeyScheduling();

  return (
    <>
      <SchedulingPageStack>
        {schedulingItems?.length > 2 ? (
          <Typography color="error" textAlign="center">
            you cannot create more than 3 schedules
          </Typography>
        ) : null}
        <Typography fontSize="16px" fontWeight="600" paddingLeft="16px">
          Scheduling
        </Typography>

        {schedulingItems.map((item, index) => (
          <Stack
            key={`${item?.id ?? ""}-${index}`}
            className="borderStack"
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            position="relative"
          >
            <Stack width="95%" gap="18px">
              {item?.id && (
                <IconButton
                  sx={{
                    position: "absolute",
                    bottom: "20px",
                    right: "20px",
                    color: "#1976D2",
                  }}
                  onClick={() => setConfirmationDialog(item?.id)}
                >
                  <DeleteIcon />
                </IconButton>
              )}
              <Stack direction="row" justifyContent="space-between">
                <TextField
                  sx={{ width: "25%" }}
                  label="Name"
                  type="text"
                  value={item?.name}
                  onChange={(e) => handleSchedulingChange(e, "name", index)}
                  helperText={errors?.schedulingItems?.[index]?.name?.message}
                  error={errors?.schedulingItems?.[index]?.name}
                />
                <TextField
                  sx={{ width: "30%" }}
                  label="Email"
                  type="email"
                  value={item?.email?.join(",")} // Convert array to string with commas
                  onChange={(e) => handleSchedulingChange(e, "email", index)}
                  helperText={
                    errors?.schedulingItems?.[index]?.email?.find(
                      (i) => i?.message
                    )?.message
                  }
                  error={errors?.schedulingItems?.[index]?.email}
                />

                <FormControl sx={{ width: "200px" }}>
                  <InputLabel error={errors?.schedulingItems?.[index]?.hour}>
                    Hour
                  </InputLabel>
                  <Select
                    labelId="demo-select-label"
                    id="demo-select"
                    value={item?.hour}
                    onChange={(e) => handleSchedulingChange(e, "hour", index)}
                    // error={errors?.schedulingItems?.[index]?.hour}
                    input={<OutlinedInput label="Hour" />}
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                        },
                      },
                    }}
                  >
                    {[...Array(24)].map((_, index) => {
                      const hour = index;
                      const time = `${hour < 10 ? "0" : ""}${hour}:00`;
                      return (
                        <MenuItem key={time} value={time}>
                          {time}
                        </MenuItem>
                      );
                    })}
                  </Select>
                  <FormHelperText
                    error={errors?.schedulingItems?.[index]?.hour}
                  >
                    {errors?.schedulingItems?.[index]?.hour?.message}
                  </FormHelperText>
                </FormControl>
                <FormControl sx={{ width: "200px" }}>
                  <InputLabel error={errors?.schedulingItems?.[index]?.minute}>
                    Minutes
                  </InputLabel>
                  <Select
                    labelId="demo-select-label"
                    id="demo-select"
                    value={item?.minute}
                    onChange={(e) => handleSchedulingChange(e, "minute", index)}
                    // error={errors?.schedulingItems?.[index]?.minute}
                    input={<OutlinedInput label="Minutes" />}
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                        },
                      },
                    }}
                  >
                    {[...Array(60)].map((_, index) => {
                      const minute = index;
                      const time = `00:${minute < 10 ? "0" : ""}${minute}`;
                      return (
                        <MenuItem key={time} value={time}>
                          {time}
                        </MenuItem>
                      );
                    })}
                  </Select>
                  <FormHelperText
                    error={errors?.schedulingItems?.[index]?.minute}
                  >
                    {errors?.schedulingItems?.[index]?.minute?.message}
                  </FormHelperText>
                </FormControl>
              </Stack>
              <LoadingButton
                variant="contained"
                sx={{
                  height: "40px",
                  width: "90px",
                }}
                onClick={() => handleSaveScheduling(index)}
                loading={scheduleLoading?.includes(index)}
                disabled={!formEdit?.includes(index)}
              >
                save
              </LoadingButton>
            </Stack>
          </Stack>
        ))}

        <Stack direction="row" padding="24px">
          <Button
            variant="contained"
            onClick={handleAddScheduling}
            disabled={schedulingItems?.length > 2}
          >
            + Add Scheduling
          </Button>
        </Stack>
        {confirmationDialog ? (
          <ConfirmationDialog
            open={!!confirmationDialog}
            confirmationText={"Are you sure you want to delete this Schedule?"}
            handleCloseConfirmation={() => setConfirmationDialog(null)}
            onSuccess={() => handleDeleteScheduling(confirmationDialog)}
          />
        ) : null}
      </SchedulingPageStack>

      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={openSnackbar}
        autoHideDuration={3000}
        onClose={() => {
          setOpenSnackbar(false);
        }}
      >
        <SnackbarContent
          style={{
            backgroundColor:
              snackBarMsg === "Saved Successfully" ||
              snackBarMsg === "Edited Successfully"
                ? "#228B22"
                : "#CA3433",
          }}
          message={snackBarMsg}
        />
      </Snackbar>
    </>
  );
}

const SchedulingPageStack = styled(Stack)(() => ({
  width: " 100%",
  background: "#ffffff",
  border: "none",
  padding: "20px",
  outline: "none",
  height: "calc(100vh - 160px)",
  overflowY: "scroll",
  overflowX: "hidden",
  boxShadow: "1px 4px 10px rgba(48, 111, 188, 0.2)",
  gap: "24px",
  ".borderStack": {
    gap: "16px",
    padding: "24px",
    boxShadow: "1px 4px 10px rgba(48, 111, 188, 0.2)",
  },
}));
