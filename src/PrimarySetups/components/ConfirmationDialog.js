import {
  Dialog,
  DialogTitle,
  styled,
  DialogContent,
  Button,
  Stack,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React from "react";
import WarningIcon from "@mui/icons-material/Warning";

export default function ConfirmationDialog({
  open,
  handleCloseConfirmation,
  confirmationText,
  onSuccess,
  warningIcon = false,
}) {
  return (
    <StyledChannelDialog
      open={open}
      onClose={handleCloseConfirmation}
      maxWidth="sm"
      fullWidth
    >
      <DialogTitle className="content">
        {warningIcon ? (
          <Stack flexDirection="row" paddingBottom={2.5} alignItems="center">
            <Typography variant="h6" fontWeight="500">
              Warning
            </Typography>
            <WarningIcon sx={{ color: "#a90f0f", ml: 1 }} />{" "}
          </Stack>
        ) : null}
        {confirmationText}
      </DialogTitle>
      <CloseIcon className="closeButton" onClick={handleCloseConfirmation} />
      <DialogContent className="content">
        <Stack direction="row" gap="24px">
          <Button variant="contained" onClick={handleCloseConfirmation}>
            No
          </Button>
          <Button variant="contained" onClick={onSuccess}>
            Yes
          </Button>
        </Stack>
      </DialogContent>
    </StyledChannelDialog>
  );
}

const StyledChannelDialog = styled(Dialog)(() => ({
  ".closeButton": {
    position: "absolute",
    right: "10px",
    top: "10px",
    cursor: "pointer",
  },
  ".content": {
    display: "flex",
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    overflow: "visible",
    textAlign: "center",
  },
}));
