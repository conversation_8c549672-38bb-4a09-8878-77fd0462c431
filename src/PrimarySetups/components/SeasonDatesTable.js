import React, { useState, Fragment, useMemo, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Collapse,
  Box,
  IconButton,
  Typography,
  MenuItem,
  Select,
  Snackbar,
  SnackbarContent,
  TextField,
  styled,
  tableCellClasses,
  Stack,
  Button,
} from "@mui/material";
import { DatePicker } from "../../sdk";
import { Add, Remove, Edit } from "@mui/icons-material";

const seasons = {
  high: "High",
  low: "Low",
};

export default function SeasonDatesTable({
  seasonDetails,
  onChange,
  open,
  handleCloseStatus,
  networkMsg,
  onRemoveRowHandler,
  onAddRowHandler,
  handleSaveSeasonDetails,
  handleCancelEdits,
  isNewRowAdded,
  setNewRowAdded,
}) {
  const [expandedYears, setExpandedYears] = useState({});
  const [editingYears, setEditingYears] = useState({});
  // Function to extract the academic year from a date
  const getAcademicYear = (endDate) => {
    const year = endDate?.getFullYear();
    const month = endDate?.getMonth() + 1; // JavaScript months are 0-indexed

    // If end date is in 2021, it belongs to 2020-2021 academic year
    // If end date is in 2022, it belongs to 2021-2022 academic year, and so on
    return `${year - 1}-${year}`;
  };

  // Group data by academic year
  const groupedData = useMemo(() => {
    const arr = seasonDetails.reduce((acc, row) => {
      const academicYear = getAcademicYear(row.endDate);
      if (!acc[academicYear]) {
        acc[academicYear] = [];
      }
      acc[academicYear].push(row);
      return acc;
    }, {});
    return arr;
  }, [seasonDetails]);

  // Handle accordion toggle - allows multiple sections to be open
  const toggleYear = (year) => {
    setExpandedYears((prev) => ({
      ...prev,
      [year]: !prev[year],
    }));

    setEditingYears((prev) => ({
      ...prev,
      [year]: false,
    }));
  };

  // Sort years in descending order (most recent first)
  const sortedYears = Object.keys(groupedData).sort();

  // Find the original index of a row in the seasonDetails array
  const findOriginalIndex = (year, rowIndex) => {
    let count = 0;
    for (const y of Object.keys(groupedData)) {
      if (y === year) {
        return seasonDetails.indexOf(groupedData[y][rowIndex]);
      }
      count += groupedData[y].length;
    }
    return -1;
  };
  const toggleEditModeForYear = (year, event) => {
    // Stop propagation to prevent accordion from toggling
    if (event) event.stopPropagation();
    setEditingYears((prev) => ({
      ...prev,
      [year]: !prev[year],
    }));
  };

  // Handle save for a specific year
  const handleSaveForYear = (year, event) => {
    event.stopPropagation();
    // You can add additional logic here if needed before calling the main save handler
    handleSaveSeasonDetails();
    // Turn off editing for this year
    setEditingYears((prev) => ({
      ...prev,
      [year]: false,
    }));
  };

  // Handle cancel for a specific year
  const handleCancelForYear = (year, event) => {
    event.stopPropagation();
    handleCancelEdits();
    // Turn off editing for this year
    setEditingYears((prev) => ({
      ...prev,
      [year]: false,
    }));
  };

  const showSeasonDates = useMemo(() => {
    if (Object.entries(editingYears)?.find(([key, value]) => value))
      return true;
    return !!seasonDetails?.length;
  }, [editingYears, seasonDetails.length]);

  const handleAddDate = () => {
    const year = getAcademicYear(new Date());
    toggleYear(year);
    toggleEditModeForYear(year);
    setNewRowAdded(false);
  };
  useEffect(() => {
    if (isNewRowAdded) handleAddDate();
  }, [isNewRowAdded]);

  useEffect(() => {
    //handle side effect when last season date is removed
    if (seasonDetails?.length) return;
    if (
      seasonDetails?.length === 0 &&
      Object.entries(editingYears)?.some(([key, value]) => value)
    )
      handleSaveSeasonDetails();
  }, [editingYears, handleSaveSeasonDetails, seasonDetails?.length]);

  return (
    <StyledContainer>
      <TableContainer
        component={Paper}
        sx={{ maxHeight: "50vh" }}
        className="container"
      >
        {showSeasonDates ? (
          <Table
            stickyHeader
            aria-label="season dates table"
            sx={{
              width: "100%",
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "8px 0px 0px 0px",
            }}
          >
            <TableBody>
              {sortedYears.map((year) => (
                <Fragment key={`${year}-seasonDates`}>
                  {/* Year row (accordion header) */}
                  <TableRow
                    sx={{
                      cursor: "pointer",
                      backgroundColor: expandedYears[year]
                        ? "rgba(0, 0, 0, 0.04)"
                        : "inherit",
                    }}
                    onClick={() => toggleYear(year)}
                  >
                    <TableCell sx={{ padding: "6px 16px" }}>
                      <Stack
                        justifyContent="space-between"
                        alignItems="center"
                        flexDirection="row"
                      >
                        <Typography variant="subtitle1" fontWeight={500}>
                          {year}
                        </Typography>
                        <Stack flexDirection="row">
                          {expandedYears[year] ? (
                            !editingYears[year] ? (
                              <IconButton
                                onClick={(e) => toggleEditModeForYear(year, e)}
                                size="small"
                              >
                                <Edit />
                              </IconButton>
                            ) : (
                              <div>
                                <Button
                                  onClick={(e) => handleCancelForYear(year, e)}
                                  variant="outlined"
                                  color="error"
                                  size="small"
                                  sx={{ mr: 1 }}
                                >
                                  Cancel
                                </Button>
                                <Button
                                  onClick={(e) => handleSaveForYear(year, e)}
                                  variant="contained"
                                  color="primary"
                                  size="small"
                                  sx={{
                                    backgroundColor: "#306fbc",
                                    color: "white",
                                    fontWeight: "bold",
                                    fontFamily: "Roboto",
                                  }}
                                >
                                  Save
                                </Button>
                              </div>
                            )
                          ) : null}
                          <IconButton size="small">
                            {expandedYears[year] ? <Remove /> : <Add />}
                          </IconButton>
                        </Stack>
                      </Stack>
                    </TableCell>
                  </TableRow>

                  {/* Expanded content */}
                  <TableRow>
                    <StyledTableCell style={{ padding: 0 }} colSpan={2}>
                      <Collapse
                        in={expandedYears[year]}
                        timeout="auto"
                        unmountOnExit
                      >
                        <Box sx={{ margin: 1 }}>
                          <Table size="small">
                            <TableHead>
                              <TableRow>
                                <StyledTableCell>Start Date</StyledTableCell>
                                <StyledTableCell>End Date</StyledTableCell>
                                <StyledTableCell>Season Dates</StyledTableCell>
                                {editingYears[year] && (
                                  <StyledTableCell>Actions</StyledTableCell>
                                )}
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {groupedData[year].map((row, idx) => {
                                // Find the original index in seasonDetails for handlers
                                const originalIndex = findOriginalIndex(
                                  year,
                                  idx
                                );

                                return (
                                  <TableRow key={idx}>
                                    <StyledTableCell>
                                      {editingYears[year] ? (
                                        <DatePicker
                                          inputFormat={"dd/MM/yyyy"}
                                          value={row.startDate}
                                          onChange={(sDate) =>
                                            onChange(
                                              originalIndex,
                                              "startDate",
                                              sDate
                                            )
                                          }
                                          renderInput={(params) => (
                                            <TextField {...params} />
                                          )}
                                        />
                                      ) : (
                                        row.startDate.toLocaleDateString(
                                          "en-GB"
                                        )
                                      )}
                                    </StyledTableCell>
                                    <StyledTableCell>
                                      {editingYears[year] ? (
                                        <DatePicker
                                          inputFormat={"dd/MM/yyyy"}
                                          value={row.endDate}
                                          onChange={(eDate) =>
                                            onChange(
                                              originalIndex,
                                              "endDate",
                                              eDate
                                            )
                                          }
                                          renderInput={(params) => (
                                            <TextField {...params} />
                                          )}
                                        />
                                      ) : (
                                        row.endDate.toLocaleDateString("en-GB")
                                      )}
                                    </StyledTableCell>
                                    <StyledTableCell>
                                      {editingYears[year] ? (
                                        <Select
                                          value={row.season || ""}
                                          onChange={(e) =>
                                            onChange(
                                              originalIndex,
                                              "season",
                                              e.target.value
                                            )
                                          }
                                        >
                                          <MenuItem
                                            value={seasons.high.toLowerCase()}
                                          >
                                            {seasons.high}
                                          </MenuItem>
                                          <MenuItem
                                            value={seasons.low.toLowerCase()}
                                          >
                                            {seasons.low}
                                          </MenuItem>
                                        </Select>
                                      ) : row.season === "high" ? (
                                        seasons.high
                                      ) : (
                                        row.season === "low" && seasons.low
                                      )}
                                    </StyledTableCell>

                                    {editingYears[year] && (
                                      <StyledTableCell>
                                        <IconButton
                                          onClick={() =>
                                            onAddRowHandler(row?.endDate)
                                          }
                                          size="small"
                                        >
                                          <Add />
                                        </IconButton>

                                        <IconButton
                                          onClick={() => {
                                            onRemoveRowHandler(originalIndex);
                                          }}
                                          size="small"
                                        >
                                          <Remove />
                                        </IconButton>
                                      </StyledTableCell>
                                    )}
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </Box>
                      </Collapse>
                    </StyledTableCell>
                  </TableRow>
                </Fragment>
              ))}
            </TableBody>
          </Table>
        ) : null}
      </TableContainer>

      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={open}
        autoHideDuration={3000}
        onClose={handleCloseStatus}
      >
        <SnackbarContent
          style={{ backgroundColor: "#CA3433" }}
          message={networkMsg}
        />
      </Snackbar>
    </StyledContainer>
  );
}

const StyledContainer = styled("div")(({ theme }) => ({
  padding: "0 16px",
  ".container": {
    height: "330px",
    "-ms-overflow-style": "none" /* Internet Explorer 10+ */,
    "scrollbar-width": "none" /* Firefox */,
  },
  ".container::-webkit-scrollbar": {
    display: "none" /* Safari and Chrome */,
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    color: "#ffffff",
    background: "#306fbc",
    textAlign: "center",
  },
  [`&.${tableCellClasses.body}`]: {
    padding: " 6px 10px",
    textAlign: "center",
  },
}));
