import { Box } from "@mui/material";
import React, { useEffect, useMemo } from "react";
import { useHistory, useParams } from "react-router-dom";
import { styled } from "@mui/system";
import { useWarning } from "../../Provider/context";
import { useAuth } from "../../sdk";

const Test = styled(Box)`
  &:-webkit-any-link {
    text-decoration: none;
  }
  font: normal normal bold 14px/18px Roboto;
  letter-spacing: 0px;

  &:hover {
    cursor: pointer;
  }
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #ffffff;
  box-shadow: 0px 4px 4px rgba(48, 111, 188, 0.2);
  border-radius: 8px;
  color: #306fbc;
`;
const TabNav = (props) => {
  const { formEdit, setformEdit, handleRedirect } = useWarning();
  const { children, to, activeStyle } = props;

  return (
    <Test
      onClick={() => {
        handleRedirect(to);
      }}
      style={{
        ...activeStyle,
      }}
    >
      {children}
    </Test>
  );
};
const Header = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 40px;
  font: normal normal bold 16px/20px Roboto;
  letter-spacing: 0px;
  color: #281e53;
  margin: auto;
  margin-bottom: 20px;
  gap: 20px;
`;
const HeaderCard = styled(Box)`
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  background: white;
  height: 40px;
  flex: 1;
  border-radius: 8px;
`;
const activeTabStyle = {
  color: "#ffffff",
  background: "#306FBC",
  boxShadow: "0px 4px 4px rgba(48, 111, 188, 0.2)",
  borderRadius: "8px",
};

export const PrimarySetup = ({ rateShopAppEnabled }) => {
  const { hotelId } = useParams();
  const { permission, token, version, currentHotel } = useAuth();
  const { formEdit, setformEdit, handleRedirect } = useWarning();
  const history = useHistory();
  let seasonSetup = null,
    hotelDetailsView = null,
    multiplier = null,
    DLF = null,
    compDetailsView = null,
    hotelDetailsEdit = null;
  useEffect(() => {
    if (token && hotelId) {
      window.scrollTo(0, 0);
    } else {
      return;
    }
  }, [token, hotelId]);
  for (let key in permission) {
    if (permission.hasOwnProperty(key)) {
      if (permission[key].name === "seasonSetup") {
        seasonSetup = permission[key];
      }
      if (permission[key].name === "hotelDetailsView") {
        hotelDetailsView = permission[key];
      }
      if (permission[key].name === "compDetailsView") {
        compDetailsView = permission[key];
      }
      if (permission[key].name === "DLF") {
        DLF = permission[key];
      }
      if (permission[key].name === "multiplier") {
        multiplier = permission[key];
      }
      if (permission[key].name === "hotelDetailsEdit") {
        hotelDetailsEdit = permission[key];
      }
    }
  }

    const showPmsTab=useMemo(()=>{
      if( currentHotel?.innKeyEnabled){
        if(currentHotel?.isChannelManagerEnabled) return false;
          return true;
      }
      return false
    },[currentHotel?.innKeyEnabled, currentHotel?.isChannelManagerEnabled])
  return (
    <Header>
      {hotelDetailsView && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup`}
              activeStyle={
                history.location.pathname === `/hotel/${hotelId}/setup`
                  ? activeTabStyle
                  : {}
              }
            >
              Hotel Details
            </TabNav>
          ) : (
            <TabNav exact to={`/hotel`}>
              Hotel Details
            </TabNav>
          )}
        </HeaderCard>
      )}
      {hotelDetailsEdit && currentHotel?.rateShopEnabled && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/scheduling`}
              activeStyle={
                history.location.pathname ===
                `/hotel/${hotelId}/setup/scheduling`
                  ? activeTabStyle
                  : {}
              }
            >
              RateShop Scheduling
            </TabNav>
          ) : (
            <TabNav exact to={`/hotel`}>
              RateShop Scheduling
            </TabNav>
          )}
        </HeaderCard>
      )}
      {hotelDetailsEdit && currentHotel?.rateShopEnabled && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/room-mapping`}
              activeStyle={
                history.location.pathname?.includes(
                  `/hotel/${hotelId}/setup/room-mapping`
                )
                  ? activeTabStyle
                  : {}
              }
            >
              RateShop Room Mapping
            </TabNav>
          ) : (
            <TabNav exact to={`/hotel`}>
              RateShop Room Mapping
            </TabNav>
          )}
        </HeaderCard>
      )}
      {hotelDetailsEdit && currentHotel?.isChannelManagerEnabled && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/channel-manager`}
              activeStyle={
                history.location.pathname ===
                `/hotel/${hotelId}/setup/channel-manager`
                  ? activeTabStyle
                  : {}
              }
            >
              Channel Manager
            </TabNav>
          ) : (
            <TabNav exact to={`/hotel`}>
              Channel Manager
            </TabNav>
          )}
        </HeaderCard>
      )}
      {hotelDetailsEdit && ( //compDetailsView
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/competitors`}
              activeStyle={
                history.location.pathname.includes("/setup/competitors")
                  ? activeTabStyle
                  : {}
              }
            >
              {" "}
              Competitor Setup
            </TabNav>
          ) : (
            <TabNav exact to={`#`}>
              Competitor Setup
            </TabNav>
          )}
        </HeaderCard>
      )}
      {hotelDetailsEdit && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/room-adjustment`}
              activeStyle={
                history.location.pathname.includes("/setup/room-adjustment")
                  ? activeTabStyle
                  : {}
              }
            >
              {" "}
              Room Adjustment
            </TabNav>
          ) : (
            <TabNav exact to={`#`}>
              Room Adjustment
            </TabNav>
          )}{" "}
        </HeaderCard>
      )}
      {currentHotel?.innKeyEnabled && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/innkkey-scheduling`}
              activeStyle={
                history.location.pathname ===
                `/hotel/${hotelId}/setup/innkkey-scheduling`
                  ? activeTabStyle
                  : {}
              }
            >
              Innkey Scheduling
            </TabNav>
          ) : (
            <TabNav exact to={`/hotel`}>
              Innkey Scheduling
            </TabNav>
          )}
        </HeaderCard>
      )}
      {showPmsTab && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/pms`}
              activeStyle={
                history.location.pathname ===
                `/hotel/${hotelId}/setup/pms`
                  ? activeTabStyle
                  : {}
              }
            >
              PMS Interface Setup
            </TabNav>
          ) : (
            <TabNav exact to={`/hotel`}>
              PMS Interface Setup
            </TabNav>
          )}
        </HeaderCard>
      )}

      {version === "v1" ? (
        ""
      ) : version === "v2" && (hotelDetailsView || hotelDetailsEdit) ? (
        <>
          <HeaderCard>
            {hotelId ? (
              <TabNav
                exact
                to={`/hotel/${hotelId}/setup/market-segment`}
                activeStyle={
                  history.location.pathname.includes("/setup/market-segment")
                    ? activeTabStyle
                    : {}
                }
              >
                {" "}
                Market Segment
              </TabNav>
            ) : (
              <TabNav exact to={`#`}>
                Market Segment
              </TabNav>
            )}{" "}
          </HeaderCard>
          <HeaderCard>
            {hotelId ? (
              <TabNav
                exact
                to={`/hotel/${hotelId}/setup/budget-setup`}
                activeStyle={
                  history.location.pathname.includes("/setup/budget-setup")
                    ? activeTabStyle
                    : {}
                }
              >
                {" "}
                Budget Setup
              </TabNav>
            ) : (
              <TabNav exact to={`#`}>
                Budget Setup
              </TabNav>
            )}{" "}
          </HeaderCard>
          <HeaderCard>
            {hotelId ? (
              <TabNav
                exact
                to={`/hotel/${hotelId}/setup/budget-allocation`}
                activeStyle={
                  history.location.pathname.includes("/setup/budget-allocation")
                    ? activeTabStyle
                    : {}
                }
              >
                {" "}
                Budget Allocation
              </TabNav>
            ) : (
              <TabNav exact to={`#`}>
                Budget Allocation
              </TabNav>
            )}{" "}
          </HeaderCard>
        </>
      ) : (
        ""
      )}

      {hotelDetailsEdit && ( //seasonSetup
        <HeaderCard>
          {hotelId ? (
            <TabNav
              exact
              to={`/hotel/${hotelId}/setup/hotel-pricing`}
              activeStyle={
                history.location.pathname.includes("/setup/hotel-pricing")
                  ? activeTabStyle
                  : {}
              }
            >
              {" "}
              Season Setup
            </TabNav>
          ) : (
            <TabNav exact to={`#`}>
              Season Setup
            </TabNav>
          )}{" "}
        </HeaderCard>
      )}
      {hotelDetailsEdit && DLF && multiplier && (
        <HeaderCard>
          {hotelId ? (
            <TabNav
              to={`/hotel/${hotelId}/setup/algorithm`}
              activeStyle={
                history.location.pathname.includes("/setup/algorithm")
                  ? activeTabStyle
                  : {}
              }
            >
              {" "}
              Algorithm Setup
            </TabNav>
          ) : (
            <TabNav exact to={`#`}>
              Algorithm Setup
            </TabNav>
          )}
        </HeaderCard>
      )}
    </Header>
  );
};
