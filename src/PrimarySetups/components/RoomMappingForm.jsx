import React, { useCallback } from "react";
import {
  Stack,
  TextField,
  Checkbox,
  Typography,
  IconButton,
  FormControl,
  FormLabel,
} from "@mui/material";
import DeleteIcon from "@mui/icons-material/Delete";
import { LoadingButton } from "@mui/lab";

const roomFieldConfig = [
  { key: "roomId", label: "Room ID", type: "number" },
  { key: "roomName", label: "Room name", type: "text" },
  { key: "pmsRoomTypeId", label: "PMS Room Type Id", type: "text" },
  { key: "roomRateGroupCode", label: "Rate Code Name", type: "text" },
  { key: "pmsRateCodeId", label: "PMS Rate Code ID", type: "number" },
  { key: "pmsRateCodeType", label: "PMS Rate Code Type", type: "text" },
  { key: "discount", label: "Promotional Discount", type: "number" },
  {
    key: "addToBasePrice",
    label: "Add to base price (+/-)",
    type: "number",
    conditional: true,
  },
  {
    key: "maxThresholdWeekday",
    label: "Max Threshold Weekday",
    type: "number",
  },
  {
    key: "minThresholdWeekday",
    label: "Min Threshold Weekday",
    type: "number",
  },
  {
    key: "maxThresholdWeekend",
    label: "Max Threshold Weekend",
    type: "number",
  },
  {
    key: "minThresholdWeekend",
    label: "Min Threshold Weekend",
    type: "number",
  },
];

export const RoomMappingForm = ({
  room,
  index,
  handleSaveRooms,
  loaderRoomIndex,
  isLoading,
  register,
  errors,
  handleDeleteRoom,
  watch,
  formState,
  setValue,
}) => {
  const isSaveForRoomDisabled = useCallback(
    (index) => {
      const dirtyRoomFields = formState?.dirtyFields?.roomFields?.[index];
      const currRoom = watch("roomFields")?.[index];
      const isDirty =
        dirtyRoomFields && Object.keys(dirtyRoomFields).length > 0;
      const isValid =
        currRoom?.pmsRoomTypeId &&
        currRoom?.pmsRateCodeId &&
        currRoom?.roomName;
      return !(isDirty && isValid);
    },
    [formState, watch]
  );
  const fieldError = useCallback(
    (field) => errors?.roomFields?.[index]?.[field.key],
    [errors?.roomFields, index]
  );
  return (
    <Stack className="borderStack" gap="16px">
      <Stack
        flexDirection="row"
        alignItems="center"
        justifyContent="space-between"
      >
        <Typography className="pageHeading">Room Mapping</Typography>
        {!room?.isNewRoom && (
          <IconButton
            sx={{
              color: "#1976D2",
              ml: "40px",
            }}
            disableRipple
            onClick={handleDeleteRoom}
          >
            <DeleteIcon />
          </IconButton>
        )}
      </Stack>
      <Stack gap="16px" flexDirection={"row"}>
        <Stack
          display="flex"
          flexGrow="1"
          flexDirection="row"
          gap="16px"
          flexWrap="wrap"
        >
          {roomFieldConfig.map((field) => {
            if (field?.conditional && room?.isBaseRoom) return null;
            return (
              <TextField
                key={field.key}
                label={field.label}
                type={field.type}
                // error={Boolean(fieldError(field))}
                // helperText={fieldError(field)?.message}
                {...register(`roomFields.${index}.${field.key}`)}
              />
            );
          })}
        </Stack>
        <Stack justifyContent="space-between">
          <FormControl
            sx={{
              flexDirection: "row",
              alignItems: "center",
            }}
          >
            <Checkbox
              color="primary"
              // {...register(`roomFields.${index}.isBaseRoom`)}
              onChange={(e) => {
                setValue(`roomFields.${index}.isBaseRoom`, e.target.checked, {
                  shouldDirty: true,
                });
              }}
              checked={room?.isBaseRoom}
            />
            <FormLabel
              sx={{
                width: "max-content",
              }}
            >
              Is Base Room
            </FormLabel>
          </FormControl>
          <LoadingButton
            variant="contained"
            onClick={() => handleSaveRooms(index)}
            disabled={isSaveForRoomDisabled(index)}
            loading={isLoading && loaderRoomIndex === index}
          >
            Save
          </LoadingButton>
        </Stack>
      </Stack>
    </Stack>
  );
};

export default RoomMappingForm;
