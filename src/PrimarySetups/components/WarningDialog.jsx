import React from "react";
import {
  Dialog,
  DialogTitle,
  <PERSON>alog<PERSON>ontent,
  Stack,
  Button,
  Typography,
  styled,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import WarningIcon from "@mui/icons-material/Warning";

export const WarningDialog = ({ open, onClose, onConfirm }) => {
  return (
    <StyledDialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className="content">
        <Stack direction="row" alignItems="center" gap="8px">
          <Typography fontWeight="600" fontSize="24px">
            Warning
          </Typography>
          <WarningIcon sx={{ color: "#a90f0f" }} />
        </Stack>
      </DialogTitle>

      <IconButton aria-label="close" className="closeButton" onClick={onClose}>
        <CloseIcon />
      </IconButton>

      <DialogContent className="content">
        <Typography textAlign="center">
          Are you sure you want to disable the connection, as this might create
          problems in fetching and pushing the ARR to the Channel Manager?
        </Typography>
        <Stack direction="row" gap="40px" padding="24px">
          <Button variant="contained" onClick={onClose}>
            No
          </Button>
          <Button variant="contained" onClick={onConfirm}>
            Yes
          </Button>
        </Stack>
      </DialogContent>
    </StyledDialog>
  );
};

const StyledDialog = styled(Dialog)(() => ({
  ".closeButton": {
    position: "absolute",
    right: "10px",
    top: "10px",
    cursor: "pointer",
  },
  ".content": {
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
  },
}));

export default WarningDialog;
