import { useCallback, useEffect, useState } from "react";
import { useAuth } from "../../sdk";
import { useParams } from "react-router-dom";
import { useWarning } from "../../Provider/context";
import { useForm } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

const roomFieldsSchema = yup.object({
  roomFields: yup.array().of(
    yup.object({
      id: yup.string().nullable(),
      roomId: yup
        .number()
        .required("Room id is required")
        .typeError("Room id must be a number"),
      pmsRoomTypeId: yup
        .number()
        .required("PMS room type id is required")
        .typeError("PMS room type id must be a number"),
      roomName: yup
        .string()
        .required("PMS room name is required")
        .typeError("PMS room name must be a string"),
      roomRateGroupCode: yup
        .number()
        .required("Rate rate group code is required")
        .typeError("Rate rate group code must be a number"),
      discount: yup
        .number()
        .required("Promotional Discount is required")
        .typeError("Promotional Discount must be a number"),
      addToBasePrice: yup
        .number()
        .typeError("Add to base price must be a number"),
      maxThresholdWeekday: yup
        .number()
        .typeError("Max Threshold Weekday must be a number"),
      minThresholdWeekday: yup
        .number()
        .typeError("Min Threshold Weekday must be a number"),
      maxThresholdWeekend: yup
        .number()
        .typeError("Max Threshold Weekend must be a number"),
      minThresholdWeekend: yup
        .number()
        .typeError("Min Threshold Weekend must be a number"),
      pmsRateCodeId: yup
        .string()
        .nullable()
        .typeError("PMS Rate Code ID must be a string"),
      pmsRateCodeType: yup
        .string()
        .nullable()
        .typeError("PMS Rate Code Type must be a string"),
    })
  ),
});

const getPayload = (values, currentHotel) => {
  const requestBody = {
    propertyId: currentHotel?.channelManagerPropertyID,
    roomId: values?.roomId ?? null,
    roomRateGroupCode: values?.roomRateGroupCode ?? null,
    roomName: values?.roomName ?? null,
    isBaseRoom: values?.isBaseRoom || false,
    discount: values?.discount ? Number(values.discount) : null,
    addToBasePrice: Number(values?.addToBasePrice ?? 0),
    maxThresholdWeekday: values?.maxThresholdWeekday
      ? Number(values.maxThresholdWeekday)
      : null,
    minThresholdWeekday: values?.minThresholdWeekday
      ? Number(values.minThresholdWeekday)
      : null,
    maxThresholdWeekend: values?.maxThresholdWeekend
      ? Number(values.maxThresholdWeekend)
      : null,
    minThresholdWeekend: values?.minThresholdWeekend
      ? Number(values.minThresholdWeekend)
      : null,
    pmsRoomTypeId: values?.pmsRoomTypeId ?? null,
    pmsRateCodeId: values?.pmsRateCodeId?.trim() ? values.pmsRateCodeId : null,
    pmsRateCodeType: values?.pmsRateCodeType ?? null,
  };
  return requestBody;
};
const initialValue = {
  roomFields: [],
};
export default function usePmsInterfaceSetup() {
  const { authFetch, currentHotel } = useAuth();
  const { hotelId } = useParams();
  const { formEdit, setformEdit, handleRedirect } = useWarning();
  const [isDeleteConfirmation, setIsDeleteConfirmation] = useState(null);

  const [isLoading, setIsLoading] = useState(false);
  const [loaderRoomIndex, setLoaderRoomIndex] = useState(null);
  const [snackbarMsg, setSnackBarMsg] = useState({
    open: false,
    msg: "",
    color: "",
  });

  const { setValue, register, watch, formState } = useForm({
    mode: "onBlur",
    resolver: yupResolver(roomFieldsSchema),
    defaultValues: initialValue,
  });

  const formData = watch("roomFields");

  const getAllRooms = useCallback(async () => {
    try {
      const { get } = await authFetch({
        path: `/hotel/${hotelId}/room-rate-plan-innkey`,
      });
      const { data, response } = await get();

      if (data && response.status === 200) {
        setValue("roomFields", data ?? []);
      }
    } catch (err) {
      console.log(err);
    }
  }, [authFetch, hotelId, setValue]);

  useEffect(() => {
    getAllRooms();
  }, [getAllRooms]);

  const handleCreateRooms = useCallback(
    async (values, index) => {
      try {
        setformEdit(false);
        setLoaderRoomIndex(index);
        setIsLoading(true);

        const requestBody = getPayload(values, currentHotel);

        const { post } = await authFetch({
          path: `/hotel/${hotelId}/create-room-rate-plan-innkey`,
        });

        const { response } = await post(requestBody);

        if (response.status === 200) {
          getAllRooms();
          setSnackBarMsg({
            open: true,
            msg: "created Successfully",
            color: "#228B22",
          });
        } else {
          setSnackBarMsg({
            open: true,
            msg: response?.data.messageToUser ?? "Something went wrong",
            color: "#CA3433",
          });
        }
      } catch (err) {
        console.log(err);
        setSnackBarMsg({
          open: true,
          msg: err?.messageToUser ?? "Something went wrong",
          color: "#CA3433",
        });
      } finally {
        setIsLoading(false);
        setLoaderRoomIndex(null);
      }
    },
    [authFetch, currentHotel, getAllRooms, hotelId, setformEdit]
  );

  const handleUpdateRooms = useCallback(
    async (values, index) => {
      try {
        setformEdit(false);
        setLoaderRoomIndex(index);
        setIsLoading(true);
        const requestBody = getPayload(values, currentHotel);

        const { put } = await authFetch({
          path: `/hotel/${hotelId}/update-room-rate-plan-innkey/${values?.id}`,
        });

        const { response } = await put(requestBody);
        if (response.status === 200) {
          getAllRooms();
          setSnackBarMsg({
            open: true,
            msg: "Saved Successfully",
            color: "#228B22",
          });
        } else {
          setSnackBarMsg({
            open: true,
            msg: response?.data.messageToUser ?? "Something went wrong",
            color: "#CA3433",
          });
        }
      } catch (err) {
        setSnackBarMsg({
          open: true,
          msg: err?.messageToUser ?? "Something went wrong",
          color: "#CA3433",
        });
        console.log(err);
      } finally {
        setIsLoading(false);
        setLoaderRoomIndex(null);
      }
    },
    [authFetch, currentHotel, getAllRooms, hotelId, setformEdit]
  );

  const handleDeleteRoom = useCallback(
    async (id) => {
      try {
        const { del } = await authFetch({
          path: `/hotel/${hotelId}/room-rate-plan-innkey?roomRatePlanId=${id}`,
        });

        const { response } = await del();

        if (response?.ok) {
          getAllRooms();
          setSnackBarMsg({
            open: true,
            msg: "Deleted Successfully",
            color: "#228B22",
          });
        } else {
          setSnackBarMsg({
            open: true,
            msg: response?.data.messageToUser ?? "Something went wrong",
            color: "#CA3433",
          });
        }
      } catch (err) {
        setSnackBarMsg({
          open: true,
          msg: err?.messageToUser ?? "Something went wrong",
          color: "#CA3433",
        });
        console.log(err);
      } finally {
        setIsDeleteConfirmation(null);
      }
    },
    [authFetch, getAllRooms, hotelId]
  );

  const handleSaveRooms = useCallback(
    (index) => {
      formData?.[index]?.isNewRoom
        ? handleCreateRooms(formData?.[index], index)
        : handleUpdateRooms(formData?.[index], index);
    },
    [handleCreateRooms, handleUpdateRooms, formData]
  );

  return {
    hotelId,
    handleRedirect,
    currentHotel,
    setValue,
    formData,
    snackbarMsg,
    setSnackBarMsg,
    isLoading,
    formEdit,
    setformEdit,
    loaderRoomIndex,
    handleSaveRooms,
    register,
    authFetch,
    formState,
    watch,
    handleDeleteRoom,
    isDeleteConfirmation,
    setIsDeleteConfirmation,
  };
}
