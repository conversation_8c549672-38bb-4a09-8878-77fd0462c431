import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { getISODate, useAuth, usePrevious } from "../../sdk";
const EMPTY_ROW = {
  startDate: new Date(),
  endDate: new Date(new Date().setDate(new Date().getDate() + 1)),
  season: null,
};

export function useSeasonDates(copyFromHotelId, seasonDates, openSelectbar) {
  const [seasonDetails, setSeasonDetails] = useState([]);
  const [isInEditMode, setIsInEditMode] = useState(false);
  const [isNewRowAdded, setNewRowAdded] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [open, setOpen] = useState(false);
  const [Loading, setLoading] = useState(false);

  const { hotelId } = useParams();
  const { token, authFetch } = useAuth();
  const saveClicked = usePrevious(isInEditMode);
  const [Error, setError] = useState(false);

  const [networkMsg, setnetworkMsg] = useState(null);

  useEffect(() => {
    if (!token) return;
    refreshSeasonDetails();
  }, [token, hotelId]);

  const isValid = useCallback((seasonDetail) => {
    return (
      seasonDetail.startDate !== null &&
      seasonDetail.endDate !== null &&
      seasonDetail.season !== null &&
      seasonDetail.endDate > seasonDetail.startDate
    );
  }, []);
  const refreshSeasonDetails = useCallback(
    async (anotherHotelId = false) => {
      const hotelIdToUse = anotherHotelId ? anotherHotelId : hotelId;
      setLoading(true);
      const { get } = await authFetch({
        path: `/hotel/${hotelIdToUse}/season-setup/all`,
      });
      const { data } = await get();

      if (data && data.length > 0) {
        setSeasonDetails(
          data.map((data) => ({
            ...data,
            startDate: new Date(data.startDate),
            endDate: new Date(data.endDate),
            season: data.season,
          }))
        );
      }

      if (data.length === 0) setSeasonDetails([]);

      if (anotherHotelId) {
        setIsInEditMode(true);
      }
      setLoading(false);
    },
    [hotelId, authFetch]
  );

  /**
   * Handles adding a new empty row when in edit mode
   */

  const handleSaveSeasonDetails = useCallback(async () => {
    try {
      // Validate before saving
      const allValid = seasonDetails.every(isValid);
      if (!allValid) {
        setnetworkMsg("Please ensure all season entries are valid");
        setOpen(true);
        return;
      }

      // Transform and filter valid seasons for API
      const seasons = seasonDetails.filter(isValid).map((row) => ({
        ...row,
        startDate: getISODate(row.startDate),
        endDate: getISODate(row.endDate),
        season: row.season.toLowerCase(),
      }));

      // Make API request
      const { post } = await authFetch({
        path: `/hotel/${hotelId}/season-setup/all`,
      });

      const { response, data } = await post(seasons);

      // Handle response
      if (response?.ok) {
        refreshSeasonDetails();
      } else {
        const errorMessage = data?.messageToUser || "Can't update Season Setup";
        setnetworkMsg(errorMessage);
        setOpen(true);
      }
    } catch (error) {
      console.error("Failed to update season details:", error);
      setnetworkMsg("An unexpected error occurred");
      setOpen(true);
    }
  }, [seasonDetails, isValid, authFetch, hotelId, refreshSeasonDetails]);

  /**
   * Handles cancellation of edits
   */
  const handleCancelEdits = useCallback(() => {
    refreshSeasonDetails();
  }, [refreshSeasonDetails]);

  function onChange(index, key, value) {
    setSeasonDetails((prevState) => {
      return prevState.map((seasonDetail, idx) =>
        idx === index
          ? {
              ...seasonDetail,
              [key]: value,
            }
          : seasonDetail
      );
    });
  }

  function toggleEdit() {
    setIsInEditMode(!isInEditMode);
  }

  function addSeason() {
    setSeasonDetails((prevState) =>
      prevState.length === 0
        ? [EMPTY_ROW]
        : [
            ...prevState,
            {
              ...EMPTY_ROW,
              startDate: new Date(
                new Date(prevState[prevState.length - 1].endDate).setDate(
                  new Date(prevState[prevState.length - 1].endDate).getDate() +
                    1
                )
              ),
              endDate: new Date(
                new Date(prevState[prevState.length - 1].endDate).setDate(
                  new Date(prevState[prevState.length - 1].endDate).getDate() +
                    2
                )
              ),
            },
          ]
    );
  }

  function handleCloseStatus() {
    setOpen(false);
  }

  const sonRemoveRowHandler = (index) => {
    setSeasonDetails((prevState) => {
      return prevState.filter((_, idx) => idx !== index);
    });
  };

  const sonAddRowHandler = (endDate) => {
    setSeasonDetails((prevState) =>
      prevState.length === 0
        ? [EMPTY_ROW]
        : [
            ...prevState,
            {
              ...EMPTY_ROW,
              startDate: new Date(
                new Date(endDate).setDate(new Date(endDate).getDate() - 1)
              ),
              endDate: new Date(endDate),
            },
          ]
    );
  };
  const handleAddNewSeasonDate = () => {
    setNewRowAdded(true);
    setSeasonDetails((prev) => [
      ...prev,
      {
        startDate: new Date(),
        endDate: new Date(),
        season: null,
      },
    ]);
  };

  useEffect(() => {
    if (copyFromHotelId === null && !seasonDates) return;
    if (!openSelectbar) refreshSeasonDetails(copyFromHotelId);
  }, [refreshSeasonDetails, seasonDates]);

  return {
    seasonDetails,
    isInEditMode,
    setIsInEditMode,
    isSaved,
    setIsSaved,
    onChange,
    toggleEdit,
    addSeason,
    dOpen: open,
    dHandleCloseStatus: handleCloseStatus,
    dNetworkMsg: networkMsg,
    sonRemoveRowHandler,
    sonAddRowHandler,
    SeasonDatesError: Error,
    Loading,
    handleSaveSeasonDetails,
    handleCancelEdits,
    isNewRowAdded,
    setNewRowAdded,
    handleAddNewSeasonDate,
  };
}
