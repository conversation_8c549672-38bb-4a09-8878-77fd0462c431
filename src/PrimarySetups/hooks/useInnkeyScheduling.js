import { useCallback, useEffect, useState } from "react";
import { useParams } from "react-router-dom/cjs/react-router-dom.min";
import { useAuth } from "../../sdk";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";

const scheduleSchema = yup.object({
  schedulingItems: yup.array().of(
    yup.object({
      name: yup.string().required("Please enter Name"),
      email: yup.array().of(yup.string().email("Invalid email format")),
      hour: yup.string().required("Please enter Hour"),
      minute: yup.string().required("Please enter Minute"),
      id: yup.string().nullable(),
    })
  ),
});
export default function useInnkeyScheduling() {
  const { authFetch, currentHotel } = useAuth();
  const { hotelId } = useParams();
  const [formEdit, setformEdit] = useState([]);
  const [openSnackbar, setOpenSnackbar] = useState(null);
  const [snackBarMsg, setSnackBarMsg] = useState("");
  const [confirmationDialog, setConfirmationDialog] = useState(null);
  const [scheduleLoading, setscheduleLoading] = useState([]);
  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    clearErrors,
  } = useForm({
    mode: "all",
    resolver: yupResolver(scheduleSchema),
    defaultValues: {
      schedulingItems: [],
    },
  });

  const schedulingItems = useWatch({ control, name: "schedulingItems" });

  // Handle field arrays for multiple scheduling items
  const { fields, append, remove } = useFieldArray({
    control,
    name: "schedulingItems",
  });

  const getHoursFromDateISoFormat = (scheduleAt) => {
    const date = new Date(scheduleAt);

    const hours = date.getUTCHours();

    return `${String(hours).padStart(2, "0")}:00`;
  };
  const getMinutesFromDateISoFormat = (scheduleAt) => {
    const date = new Date(scheduleAt);

    const minutes = date.getUTCMinutes();

    return `00:${String(minutes).padStart(2, "0")}`;
  };
  const getInnkeyScheduling = useCallback(async () => {
    try {
      const { get } = await authFetch({
        path: `/hotel/${hotelId}/innkey-schedule`,
      });
      const { data } = await get();
      if (data && data.length > 0) {
        const schedules = data?.map((item) => ({
          ...item,
          hour: getHoursFromDateISoFormat(item?.scheduleAt),
          minute: getMinutesFromDateISoFormat(item?.scheduleAt),
        }));
        setValue("schedulingItems", schedules);
      } else {
        setValue("schedulingItems", [
          {
            id: "",
            name: "",
            emails: [],
            hour: "",
            minute: "",
          },
        ]);
      }
    } catch (err) {
      console.log(err);
    }
  }, [authFetch, hotelId, setValue]);

  useEffect(() => {
    getInnkeyScheduling();
  }, [getInnkeyScheduling]);

  const handleSchedulingChange = (e, changeField, index) => {
    setformEdit((prev) => [...prev, index]);
    let value = e.target.value;
    clearErrors(`schedulingItems.${index}.${changeField}`);
    switch (changeField) {
      case "email":
        const emailArray = value
          ? value.split(",").map((email) => email.trim())
          : [];
        setValue(`schedulingItems.${index}.email`, emailArray);
        break;
      default:
        setValue(`schedulingItems.${index}.${changeField}`, value);
        break;
    }
  };

  const checkIsValidScheduling = useCallback((values) => {
    if (values?.scheduleAt && values?.name) {
      return true;
    } else {
      return false;
    }
  }, []);

  const isValidEmail = useCallback((email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    return emailRegex.test(email);
  }, []);

  const handleSaveScheduling = useCallback(
    async (formValues, index) => {
      try {
        const item = formValues?.schedulingItems?.[index];
        setscheduleLoading((prev) => [...prev, index]);
        setformEdit((prev) => prev?.filter((i) => i !== index));
        const { post } = await authFetch({
          path: `/hotel/${hotelId}/innkey-schedule`,
        });
        const { put } = await authFetch({
          path: `/hotel/${hotelId}/innkey-schedule/${item?.id}`,
        });

        const hour = item?.hour?.split(":")?.[0];
        const minutes = item?.minute?.split(":")?.[1];
        const scheduleAt = `${hour}:${minutes}:00`;

        const values = {
          name: item?.name,
          email: item?.email,
          scheduleAt,
        };
        const isValidEmails = values?.email?.every((email) =>
          isValidEmail(email)
        );

        if (values?.email && !isValidEmails) {
          setSnackBarMsg("Invalid email address");
          setOpenSnackbar(true);
          setscheduleLoading((prev) => prev?.filter((i) => i !== index));
          return;
        }

        const isValid = checkIsValidScheduling(values);

        if (!isValid) {
          setSnackBarMsg("Fields can't be empty");
          setOpenSnackbar(true);
          setscheduleLoading((prev) => prev?.filter((i) => i !== index));
          return;
        }

        const requestBody = values;
        const { response, data } = item?.id
          ? await put(requestBody)
          : await post(requestBody);
        if (response.status === 200) {
          getInnkeyScheduling();
          setSnackBarMsg("Saved Successfully");
          setOpenSnackbar(true);
        } else {
          setSnackBarMsg(data?.messageToUser || "Something went wrong");
          setOpenSnackbar(true);
        }
      } catch (err) {
        console.log(err);
      } finally {
        setscheduleLoading((prev) => prev?.filter((i) => i !== index));
      }
    },
    [
      authFetch,
      checkIsValidScheduling,
      getInnkeyScheduling,
      hotelId,
      isValidEmail,
      setformEdit,
    ]
  );

  const handleDeleteScheduling = useCallback(
    async (id) => {
      try {
        const { del } = await authFetch({
          path: `/hotel/${hotelId}/innkey-schedule/${id}`,
        });

        const { response } = await del();
        if (response.status === 200) {
          getInnkeyScheduling();
        }
      } catch (err) {
        console.log(err);
      }
      setConfirmationDialog(null);
    },
    [authFetch, getInnkeyScheduling, hotelId]
  );
  const handleAddScheduling = useCallback(() => {
    append({
      name: "",
      email: [],
      hour: "",
      minute: "",
      id: null,
    });
  }, [append]);

  return {
    formEdit,
    handleSaveScheduling: handleSubmit(handleSaveScheduling),
    openSnackbar,
    setOpenSnackbar,
    snackBarMsg,
    scheduleLoading,
    handleAddScheduling,
    handleSchedulingChange,
    confirmationDialog,
    setConfirmationDialog,
    schedulingItems,
    errors,
    control,
    handleDeleteScheduling,
  };
}
