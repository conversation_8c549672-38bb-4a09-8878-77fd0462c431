import { Checkbox, FormControlLabel } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import { Line } from "react-chartjs-2";
import { styled } from "@mui/system";
import { useHistory, useParams } from "react-router-dom";
import { DatePicker, getISODate, LoadingPage, Nodata, useAuth } from "../sdk";
import LeadTimevsPickupTable from "./components/LeadTimevsPickupTable";
import {
  Body,
  Graph,
  Header,
  Label,
  Page,
  Segment,
  SubHeader,
  TableCard,
} from "./Styles";
import { PreciumDisabledPage } from "../sdk/components/PreciumDiabledPage";

const StyledFormcontrolLabel = styled(FormControlLabel)(({ theme }) => ({
  ".label": {
    font: "normal normal bold 16px / 20px Roboto",
    color: "#281E53",
  },
}));
export default function LeadTimevsBoB({ setPageHeader }) {
  const { token, authFetch, currentHotel } = useAuth();
  const { hotelId, DATE } = useParams();
  const [selectedData, setSelectedData] = useState([]);
  const [comparitiveData, setComparitiveData] = useState([]);
  const [graphData, setGraphData] = useState({ datasets: [], labels: [] });
  const [Loading, setLoading] = useState();
  const history = useHistory();
  const [selectedDate, setSelectedDate] = useState(() => {
    if (!isNaN(new Date(DATE).getTime())) {
      const [year, mon, day] = DATE.split("-");
      if (year && mon && day) {
        if (day && day.length === 1) {
          const validDate = [year, mon, "0" + day].join("-");
          return new Date(validDate);
        } else {
          return new Date(DATE);
        }
      } else {
        return new Date(new Date().setDate(new Date().getDate() - 1));
      }
    } else {
      return new Date(new Date().setDate(new Date().getDate() - 1));
    }
  });
  const [isComparitive, setIsComparitive] = useState(false);
  const [comparitiveDate, setComparitiveDate] = useState(
    new Date(new Date().setDate(new Date().getDate() - 8))
  );

  useEffect(() => {
    if (!token && !hotelId) {
      return;
    } else {
      window.scrollTo(0, 0);
    }
  }, [token, hotelId]);

  useEffect(() => {
    if (!token && !hotelId) {
      return;
    } else {
      fetchData();
    }
  }, [token, hotelId, selectedDate]);
  useEffect(() => {
    setPageHeader("Lead Time - Pick-Up Daily");
    return () => {
      setPageHeader("");
    };
  }, []);
  useEffect(() => {
    if (!token) {
      return;
    } else {
      if (isComparitive && comparitiveDate) {
        fetchComparitiveData();
      } else {
        setComparitiveData([]);
      }
    }
  }, [token, hotelId, comparitiveDate, isComparitive]);

  useEffect(() => {
    if (!token && !hotelId) {
      return;
    }
    history.replace(
      `/hotel/${hotelId}/analytics/lead_time_vs_pickup_daily/${
        getISODate(selectedDate).split("T")[0]
      }`
    );
  }, [selectedDate, hotelId, token]);

  useEffect(() => {
    if (!token && !hotelId) {
      return;
    } else {
      if (selectedData) dataForGraph();
    }
  }, [token, hotelId, selectedData]);
  useEffect(() => {
    if (!token && !hotelId) {
      return;
    } else {
      if (comparitiveData) dataForGraph();
    }
  }, [token, hotelId, comparitiveData]);

  const graphOptions = useMemo(() => {
    function getDetails(value, data, datasetIndex) {
      let sp = null,
        op = null,
        cp = null,
        compSet = null;
      if (datasetIndex === 1) {
        comparitiveData.forEach((data) => {
          if (value === data.leadTime) {
            op = data.overridePrice;
            sp = data.systemPrice;
            cp = data.currentPrice;
            compSet = data.compSet;
          }
        });
      } else if (datasetIndex === 0) {
        selectedData.forEach((data) => {
          if (value === data.leadTime) {
            op = data.overridePrice;
            sp = data.systemPrice;
            cp = data.currentPrice;
            compSet = data.compSet;
          }
        });
      }
      return { op, sp, cp, compSet };
    }
    return {
      maintainAspectRatio: false,
      title: {
        text: "Lead Time - Pick-Up Daily",
        display: true,
        fontFamily: "Roboto",
        fontSize: 20,
        fontColor: "black",
        fontStyle: "normal",
      },
      legend: {
        position: "bottom",
        align: "left",
        labels: {
          fontFamily: "Roboto",
          fontSize: 10,
          boxWidth: 10,
        },
      },
      tooltips: {
        bodyFontFamily: "Roboto",
        backgroundColor: "black",

        callbacks: {
          label: function (tooltipItem, data) {
            let { op, sp, cp, compSet } = getDetails(
              tooltipItem.xLabel,
              data,
              tooltipItem.datasetIndex
            );
            let array = [];
            array.push(`Pickup : ${tooltipItem.yLabel}`);
            if (op !== null) {
              array.push(
                op === -1
                  ? `Override Price : Sold Out`
                  : `Override Price : ${parseInt(op)}`
              );
            }
            if (cp !== null) {
              array.push(
                cp === -1
                  ? `Current Price : Sold Out`
                  : `Current Price : ${parseInt(cp)}`
              );
            }
            if (sp !== null) {
              array.push(
                sp === -1
                  ? `System Price : Sold Out`
                  : `System Price : ${parseInt(sp)}`
              );
            }
            if (compSet !== null) {
              compSet.map((comp) => {
                array.push(
                  comp.price === -1
                    ? comp.competitorName + " : " + "Sold Out"
                    : comp.price === -999
                    ? comp.competitorName + " : " + "-"
                    : comp.competitorName + " : " + comp.price
                );
              });
            }
            return array;
          },
        },
      },
      elements: {
        line: {
          fill: false,
          borderColor: "#FBBC05",
          pointColor: "#FBBC05",
          backgroundColor: "#FBBC05",
          tension: 0,
        },
        point: {
          borderColor: "#FBBC05",
          backgroundColor: "#FBBC05",
        },
      },
      scales: {
        xAxes: [
          {
            offset: true,
            display: true,
            gridLines: {
              display: true,
              drawTicks: true,
              drawOnChartArea: false,
            },
            ticks: {
              fontFamily: "Roboto",
              fontColor: "black",
              fontSize: 10,
            },
            scaleLabel: {
              display: true,
              labelString: "Lead Time",
              fontColor: "black",
              fontFamily: "Roboto",
            },
          },
        ],
        yAxes: [
          {
            type: "linear",
            display: true,
            position: "left",

            gridLines: {
              display: true,
              drawTicks: true,
              drawOnChartArea: true,
            },
            ticks: {
              beginAtZero: true,
              fontFamily: "Roboto",
              fontColor: "black",
              fontSize: 10,
              suggestedMin: -10,
              suggestedMax: 20,
            },
            scaleLabel: {
              display: true,
              labelString: "Pick-Up",
              fontColor: "black",
              fontFamily: "Roboto",
            },
          },
        ],
      },
    };
  }, [selectedData, comparitiveData]);

  async function fetchData() {
    setLoading(true);
    const { get } = await authFetch({
      path: `/hotel/${hotelId}/lead-time-vs-pickup/date/${
        getISODate(selectedDate).split("T")[0]
      }`,
    });
    const { data, error } = await get();
    if (data) {
      setSelectedData(data);
    } else {
      setSelectedData([]);
      console.log(error);
    }

    setLoading(false);
  }

  async function fetchComparitiveData() {
    setLoading(true);
    const offset = comparitiveDate.getTimezoneOffset();
    const ISTDate = new Date(comparitiveDate.getTime() - offset * 60 * 1000);
    const { get } = await authFetch({
      path: `/hotel/${hotelId}/lead-time-vs-pickup/date/${
        getISODate(comparitiveDate).split("T")[0]
      }`,
    });
    const { data, error } = await get();
    if (data) {
      setComparitiveData(data);
    } else {
      setComparitiveData([]);
      console.log(error);
    }

    setLoading(false);
  }

  const handleSelectedDateChange = (date) => {
    setSelectedDate(date);
  };

  const handleComparitiveDateChange = (date) => {
    setComparitiveDate(date);
  };

  function dataForGraph() {
    if (selectedData.length) {
      const array = {
        labels: selectedData.map((data) => data.leadTime),
        datasets: [
          {
            label: `${new Date(selectedDate).toLocaleDateString("en-GB")}`,
            data: selectedData.map((data) =>
              data.pickup === -1000 ? null : data.pickup
            ),
            borderColor: "#4400FF",
            pointBorderColor: "#4400FF",
            pointBackgroundColor: "#4400FF",
          },
          comparitiveData.length > 0
            ? {
                label: `${new Date(comparitiveDate).toLocaleDateString(
                  "en-GB"
                )}`,
                data: comparitiveData.map((data) =>
                  data.pickup === -1000 ? null : data.pickup
                ),
                borderColor: "#FF0093",
                pointBorderColor: "#FF0093",
                pointBackgroundColor: "#FF0093",
              }
            : undefined,
        ].filter(Boolean),
      };
      setGraphData(array);
    }
  }
  return currentHotel?.isPreciumEnabled ? (
    <Page>
      <Body>
        <Header>
          <SubHeader>
            <Segment>
              <Label>Lead Time For</Label>
              <DatePicker
                inputFormat={"dd/MM/yyyy"}
                date={selectedDate}
                onChange={handleSelectedDateChange}
              />
            </Segment>
            <Segment>
              <StyledFormcontrolLabel
                classes={{ label: "label" }}
                control={
                  <Checkbox
                    onChange={() => setIsComparitive(!isComparitive)}
                    value={isComparitive}
                    style={{
                      color: "#110641",
                    }}
                  />
                }
                label="Compare with"
              />
            </Segment>
            {isComparitive === true ? (
              <DatePicker
                inputFormat={"dd/MM/yyyy"}
                date={comparitiveDate}
                onChange={handleComparitiveDateChange}
              />
            ) : (
              <DatePicker
                inputFormat={"dd/MM/yyyy"}
                date={comparitiveDate}
                onChange={handleComparitiveDateChange}
                disabled
              />
            )}
          </SubHeader>
        </Header>
        {!Loading ? (
          selectedData.length > 0 ? (
            <div style={{ height: "85%" }}>
              <TableCard style={{ height: "70%", marginBottom: "20px" }}>
                <Graph>
                  <Line data={graphData} options={graphOptions} />
                </Graph>
              </TableCard>
              <TableCard>
                <LeadTimevsPickupTable selectedData={selectedData} />
              </TableCard>
            </div>
          ) : (
            <Nodata />
          )
        ) : (
          <LoadingPage />
        )}
      </Body>
    </Page>
  ) : (
    <PreciumDisabledPage />
  );
}
