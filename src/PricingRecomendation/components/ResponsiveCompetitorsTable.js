import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Popover,
  styled,
  tableCellClasses,
} from "@mui/material";
import { Close } from "@mui/icons-material";
import { currencies } from "../../sdk/codeConstant";

const columns = [
  {
    key: "index",
    label: () => "Sq. No.",
    value: (data, index) => index + 1,
  },
  {
    key: "competitorName",
    label: () => "Hotel Name",
  },
  {
    key: "price",
    label: (currentHotel) =>
      `Price(${
        currencies[currentHotel?.RSCurrency].symbol ?? currentHotel?.RSCurrency
      })`,
    textColor: (data, currentPrice) =>
      currentPrice < data?.price ? "green" : "red",
    value: (data) => (data?.isSoldOut ? "Sold Out" : data?.price),
  },
];
export const ResponsiveCompetitorsTable = ({
  sortedCompetitors,
  currentPrice,
  currentHotel,
}) => {
  return (
    <TableContainer
      component={Paper}
      sx={{
        padding: "18px 8px",
      }}
    >
      <Table size="small">
        <TableHead>
          <TableRow>
            {columns.map((headerColumn, index) => (
              <StyledTableCell key={headerColumn.key + index} sx={{}}>
                {headerColumn.label?.(currentHotel)}
              </StyledTableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {/* Competitors Data */}
          {sortedCompetitors?.map((competitor, compIndex) => {
            return (
              <TableRow key={compIndex}>
                {columns?.map((column, index) => (
                  <StyledTableCell
                    sx={{
                      color:
                        column?.textColor?.(competitor, currentPrice) ??
                        "inherit",
                    }}
                  >
                    {column?.value
                      ? column.value(competitor, compIndex)
                      : competitor?.[column.key]}
                  </StyledTableCell>
                ))}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    color: "#676767",
  },
  [`&.${tableCellClasses.body}`]: {
    padding: " 6px 10px",
  },
}));
