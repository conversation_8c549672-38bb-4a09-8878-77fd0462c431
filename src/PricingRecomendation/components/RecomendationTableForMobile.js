import React, { useCallback, useState } from "react";
import { getDateDDMM, LoadingPage } from "../../sdk";
import { Stack, style, styled } from "@mui/system";
import {
  Box,
  IconButton,
  Popover,
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableRow,
  Typography,
} from "@mui/material";
import { ArrowDropDown, Close } from "@mui/icons-material";
import { PopoverHeader, ResponsiveTableContainer, Thead } from "../Styles";
import { ResponsiveCompetitorsTable } from "./ResponsiveCompetitorsTable";
import PopupState, { bindPopover } from "material-ui-popup-state";
import CalcPopOver from "./CalcPopOver";
export const RecommendationTableForMobile = ({
  isLoading,
  Days,
  recommendedData,
  competitors,
  currentHotel,
}) => {
  const [showCompetitorDialog, setShowCompetitorDialog] = useState(null);
  const [selectedCompetitors, setSelectedCompetitors] = useState({
    competitors: [],
    currentPrice: null,
  });
  const [showAutomatedPricePopver, setShowAutomatedPricePopver] = useState(
    null
  );

  const getCurrentPrice = (data) => {
    if (data?.isSoldOut) return "Sold Out";

    if (data?.currentPrice === -2) return "-";
    return data?.currentPrice;
  };
  const getRoundedValue = (value) =>
    !Number.isInteger(value) ? Math.round(value + Number.EPSILON) : value;

  const renderCalculatedPrice = ({ price, isOverride, thresholdPrice }) => {
    return isOverride && thresholdPrice
      ? getRoundedValue(thresholdPrice)
      : getRoundedValue(price);
  };
  const sortedCompetitors = useCallback(
    (competitorArray) => {
      let sortedArray = competitorArray?.map((i) => ({
        ...i,
        sequence: competitors?.find((j) => j.id === i?.competitorID)?.sequence,
      }));
      return sortedArray?.sort((a, b) => a?.sequence - b?.sequence);
    },
    [competitors]
  );
  const responsiveTableColumn = [
    {
      label: "Date",
      key: "date",
      value: (data) =>
        `${getDateDDMM(data?.date)} ${Days[new Date(data?.date).getDay()]}`,
    },
    {
      label: "Occ%",
      key: "occupancy",
      value: (data) => `${Math.round(data?.occupancyPercentage ?? 0)}%`,
      style: (data) =>
        data?.color
          ? {
              background: `linear-gradient(${data.color}, #FFFFFF)`,
              borderRight: "solid 1px #FFFFFF",
              textAlign: "center",
              fontFamily: "Roboto",
              fontStyle: "normal",
              fontWeight: "400",
              fontsize: "14px",
              lineHeight: "20px",
            }
          : {},
    },
    {
      label: (
        <>
          Current
          <br /> Price
        </>
      ),
      key: "currentPrice",
      style: () => ({
        padding: "16px 0px 16px 30px",
      }),
      value: (data) => (
        <Stack flexDirection="row" alignItems="center" justifyContent="center">
          {getCurrentPrice(data)}
          <IconButton
            onClick={(e) => {
              setShowCompetitorDialog(e.currentTarget);
              setSelectedCompetitors({
                competitors: data?.competitorDetails,
                currentPrice: data?.currentPrice,
              });
            }}
          >
            <ArrowDropDown fontSize="small" />
          </IconButton>
        </Stack>
      ),
    },
    {
      label: (
        <>
          Automated <br />
          Price
        </>
      ),
      key: "automatedPrice",
      value: (data) => {
        return (
          <Box
            onClick={(e) => {
              setShowAutomatedPricePopver({
                anchor: e.currentTarget,
                data: data,
              });
            }}
          >
            {renderCalculatedPrice({
              price: Math.max(data?.calculatePrice, data?.hotelPrice),
              isOverride: data?.isOverrideCalculatedPrice,
              thresholdPrice: data?.maxThresholdPrice,
            })}{" "}
            {data.calculatePrice < data?.hotelPrice && (
              <span
                style={{
                  color: "red",
                  whiteSpace: "nowrap",
                }}
              >
                *
              </span>
            )}
          </Box>
        );
      },
    },
  ];
  if (isLoading) return <LoadingPage />;
  return (
    <ResponsiveTableContainer>
      {showAutomatedPricePopver ? (
        <PopupState variant="popover" popupId={`automatedPircePopup`}>
          {(popupState) => (
            <Popover
              {...bindPopover(popupState)}
              open={!!showAutomatedPricePopver}
              onClose={() => {
                setShowAutomatedPricePopver(null);
              }}
              anchorEl={showAutomatedPricePopver?.anchor}
              anchorOrigin={{
                vertical: "bottom",
                horizontal: "center",
              }}
              transformOrigin={{
                vertical: "top",
                horizontal: "center",
              }}
            >
              <CalcPopOver
                calc={showAutomatedPricePopver?.data?.calculationSettings}
                handlePopoverClose={() => setShowAutomatedPricePopver(null)}
                baseMinPrice={showAutomatedPricePopver?.data?.hotelPrice}
                reccPrice={
                  !Number.isInteger(
                    showAutomatedPricePopver?.data?.calculatePrice
                  )
                    ? Math.round(
                        showAutomatedPricePopver?.data?.calculatePrice +
                          Number.EPSILON
                      )
                    : showAutomatedPricePopver?.data?.calculatePrice
                }
                date={new Date(
                  showAutomatedPricePopver?.data?.date
                )?.toLocaleDateString("en-GB")}
                day={
                  Days[new Date(showAutomatedPricePopver?.data?.date)?.getDay()]
                }
                checkOverridePrice={
                  showAutomatedPricePopver?.data?.isOverrideCalculatedPrice
                }
                maxThresholdPrice={
                  showAutomatedPricePopver?.data?.maxThresholdPrice
                }
              />
            </Popover>
          )}
        </PopupState>
      ) : null}
      <PopupState variant="popover" popupId={`competitorPircePopup`}>
        {(popupState) => (
          <Popover
            {...bindPopover(popupState)}
            open={!!showCompetitorDialog}
            onClose={() => {
              setShowCompetitorDialog(null);
              setSelectedCompetitors({
                competitors: [],
                currentPrice: null,
              });
            }}
            anchorEl={showCompetitorDialog}
            anchorOrigin={{
              vertical: "bottom",
              horizontal: "center",
            }}
            transformOrigin={{
              vertical: "top",
              horizontal: "center",
            }}
          >
            <PopoverHeader>
              <Typography textAlign="center" fontWeight={500} paddingTop={2}>
                Current Price :{" "}
                {selectedCompetitors?.currentPrice === -2
                  ? "N/A"
                  : selectedCompetitors?.currentPrice}
              </Typography>
              <IconButton
                sx={{
                  position: "absolute",
                  top: "5px",
                  right: "10px",
                }}
                onClick={() => {
                  setShowCompetitorDialog(null);
                  setSelectedCompetitors({
                    competitors: [],
                    currentPrice: null,
                  });
                }}
              >
                <Close />
              </IconButton>
            </PopoverHeader>

            <ResponsiveCompetitorsTable
              currentHotel={currentHotel}
              currentPrice={selectedCompetitors?.currentPrice}
              sortedCompetitors={sortedCompetitors(
                selectedCompetitors?.competitors ?? []
              )}
            />
          </Popover>
        )}
      </PopupState>
      <Table stickyHeader aria-label="sticky table" style={{ width: "100%" }}>
        <Thead>
          <TableRow
            sx={{
              backgroundColor: "#306fbc",
              border: 0,
            }}
          >
            {responsiveTableColumn?.map((i, index) => (
              <StyledTableCell
                key={`newTCell-${index}`}
                sx={{
                  // todo- fix this
                  ...(index === 0
                    ? {
                        borderTopLeftRadius: "8px",
                      }
                    : {}),
                  ...(index === 3
                    ? {
                        borderTopRightRadius: "8px",
                      }
                    : {}),
                }}
              >
                {i.label}
              </StyledTableCell>
            ))}
          </TableRow>
        </Thead>
        <TableBody>
          {recommendedData?.pricingDetails?.map((data, rowIndex) => (
            <TableRow key={`tableRow-${rowIndex}`}>
              {responsiveTableColumn?.map((column, index) => (
                <StyledTableCell key={index} style={column?.style?.(data)}>
                  {column?.value?.(data)}
                </StyledTableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </ResponsiveTableContainer>
  );
};

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#306fbc",
    color: "#ffffff",
    fontWeight: "500",
    fontSize: "14px",
    lineHeight: "20px",
    textAlign: "center",
    letterSpacing: "0px",
    padding: "8px",
  },
  [`&.${tableCellClasses.body}`]: {
    textAlign: "center",
    padding: "16px",
    // width: "11%",
  },
}));
