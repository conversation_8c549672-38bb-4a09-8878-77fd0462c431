import React from "react";
import {
  Box,
  IconButton,
  Stack,
  styled,
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const columns = [
  //   {
  //     id: "id",
  //     label: "Room Id",
  //     renderValue: (item) => item?.roomId,
  //   },
  {
    id: "roomName",
    label: "Room Name",
    renderValue: (item) => item?.roomName,
  },
  {
    id: "pushOverridePrice",
    label: "Push Override Price",
    renderValue: (item) => item?.pushOverridePrice,
  },
];
const Header = styled(Box)`
  width: 100%;
  padding: 5px 0;
  text-align: center;
  margin-right: 10px;
`;
const UpperText = styled(Typography)`
  font: normal normal bold 16px/20px Roboto;
  letter-spacing: 0px;
  color: #130453;
  margin-bottom: 8px;
`;
const OverridePriceListPopup = ({ priceList }) => {
  return (
    <StyledStack>
      <Header>
        <UpperText>Override Price details</UpperText>

        {/* <IconButton className="closeButton" onClick={onClose}>
          <CloseIcon />
        </IconButton> */}
      </Header>
      <Stack className="container">
        <Table aria-label="purchases">
          <TableHead>
            <TableRow
              sx={{
                position: "sticky",
                top: 0,
                zIndex: 1,
              }}
            >
              {columns.map((column) => (
                <StyledTableCell
                  key={`${column?.id}-accordion`}
                  style={column?.style}
                >
                  {column.label}
                </StyledTableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {!priceList?.length && (
              <TableRow>
                <StyledTableCell colSpan={13}>
                  <Typography align="center">No data found</Typography>
                </StyledTableCell>
              </TableRow>
            )}

            {priceList?.map((item, index) => (
              <TableRow key={item?.roomId ?? index} sx={{ height: "50px" }}>
                <OverridePriceListTableRows item={item} index={index} />
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Stack>
    </StyledStack>
  );
};

const OverridePriceListTableRows = ({ item, index }) => {
  return columns?.map((column) => (
    <StyledTableCell
      key={`${column?.id}-tableCell-${index}`}
      style={{ ...(column?.style || {}), height: "auto !important" }}
    >
      {column?.renderValue(item, index)}
    </StyledTableCell>
  ));
};

const StyledStack = styled(Stack)(() => ({
  ".MuiPaper-root": {
    overflow: "auto",
  },
  ".closeButton": {
    position: "absolute",
    right: "10px",
    top: "10px",
    cursor: "pointer",
  },
  ".container": {
    padding: "7px 24px",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    overflow: "visible",
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    color: "#306fbc",
    // background: "#306fbc",
    font: "normal normal normal 11px/11px Roboto",
    border:0,
    padding:'6px',
    textAlign: "center",
    width: "300px",
  },
  [`&.${tableCellClasses.body}`]: {
    font: "normal normal normal 11px/11px Roboto",
    padding: "6px 10px",
    height: "50px !important",
    border:0,
    width: "300px",
    textAlign: "center",
  },
}));

export default OverridePriceListPopup;
