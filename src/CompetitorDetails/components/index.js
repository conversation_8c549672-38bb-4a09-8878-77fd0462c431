import { <PERSON>, <PERSON><PERSON><PERSON>, Rating } from "@mui/material";
import React from "react";
import { styled } from "@mui/system";
import { <PERSON><PERSON>, <PERSON> } from "../../sdk";

const MyCard = styled(Card)`
  width: 1116px;
  height: 140px;
  border-radius: 8px;
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16);
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  margin-left: 162px;
  padding: 0px 20px;
  margin-bottom: 24px;
`;
const Div1 = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: baseline;
  width: ${(props) => (props.inEditMode ? "70%" : "57%")};
  padding-left: 5px;
`;
const Div2 = styled(Box)`
  display: flex;
  align-items: center;
  min-width: 250px;
  justify-content: space-between;
  padding-left: 5px;
  padding-bottom: 10px;
`;
const Div3 = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 125px;
  padding-bottom: 30px;
`;
const P = styled.p`
  font-family: Roboto;
  font-size: 16px;
  line-height: 1.25;
  color: #000000;
`;
const P2 = styled.p`
  font-family: Roboto;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.25;
  color: #2c2c2c;
`;

const Btn = styled.button`
  background: none;
  outline: none;
  border: none;
  font-family: Roboto;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.25;
  color: #210d79;
`;
const Div = styled(Box)`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  padding-left: 60px;
  padding-top: ${(props) => (props.inEditMode ? "20px" : "0px")};
`;
const Divbtn = styled(Box)`
  width: 1116px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 162px;
`;
const Addbtn = styled.button`
  background-color: #f2f2f2;
  border: none;
  font-family: Roboto;
  font-size: 16px;
  font-weight: bold;
  line-height: 1.25;
  color: #343434;
`;

const AddCompetitorData = ({ datas = null, changeHandler }) => {
  console.log(datas);
  return (
    <Box>
      {datas.map((data) =>
        !data.isEditMode ? (
          <Box dataid={data.id}>
            <MyCard>
              <P>{data.id}.</P>
              <Div inEditMode={data.isEditMode}>
                <Div1 inEditMode={data.isEditMode}>
                  <P>{data.name}</P>
                  <Rating value={data.rating} readOnly />
                  <P2>{data.rooms}</P2>
                </Div1>
                <Div2>
                  <P2>Address</P2>
                  <P>{data.address}</P>
                </Div2>
                <Div3>
                  <Btn>Edit</Btn>
                  <Btn>Remove</Btn>
                </Div3>
              </Div>
            </MyCard>
          </Box>
        ) : (
          <Box dataid={data.id}>
            <MyCard>
              <P>{data.id}.</P>
              <Div inEditMode={data.isEditMode}>
                <Div1 inEditMode={data.isEditMode}>
                  <TextField
                    required
                    id="standard-required"
                    placeholder="Name"
                    name="name"
                    onChange={changeHandler}
                    value={data.name}
                  />

                  <Box>
                    <Rating
                      name="rating"
                      value={data.rating}
                      onChange={changeHandler}
                    />
                  </Box>
                  <TextField
                    id="standard-number"
                    placeholder="Number of rooms"
                    type="number"
                    InputLabelProps={{
                      shrink: true,
                    }}
                    name="rooms"
                    onChange={changeHandler}
                    value={data.rooms}
                  />
                </Div1>

                <Box>
                  <TextField
                    required
                    id="standard-required2"
                    placeholder="Address"
                    name="address"
                    onChange={changeHandler}
                    value={data.address}
                    style={{ width: "430px" }}
                  />
                </Box>

                <Div3></Div3>
              </Div>
            </MyCard>
          </Box>
        )
      )}
      <Divbtn>
        <Addbtn>+ Add Hotel</Addbtn>
        <Button next>Next</Button>
      </Divbtn>
    </Box>
  );
};

export default AddCompetitorData;
