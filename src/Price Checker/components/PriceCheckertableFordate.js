import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  Stack,
  Box,
  Paper,
} from "@mui/material";

export const PriceCheckerTableforDate = ({
  calendarData,
  selectedDateIndex,
  filters,
  currencies,
  currentHotel,
  channels,
  sourceImages,
  competitors,
}) => {

  const getHotelResult = () =>
    calendarData[selectedDateIndex]?.hotelResult?.sourceSortedArray?.find(
      ({ sourceId }) => sourceId === filters?.channelIds?.[0]
    );

  const getPriceText = (item, isBrandWebsite) => {
    if (item?.isSoldOut) return "Sold Out";
    let price = 0;
    price = isBrandWebsite ? item?.price : item?.minPrice;
    return price
      ? (currencies?.[currentHotel.RSCurrency]?.symbol ??
          currentHotel.RSCurrency) + price
      : "--";
  };

  const renderSourceImage = (sourceId) => {
    const source = channels?.find((channel) => channel.sourceId === sourceId);
    return source ? (
      <img src={sourceImages[source.name]} alt={source.name} />
    ) : null;
  };

  const renderRoomType = (
    roomType,
    isMealIncluded,
    conditionCode,
    cancellationStatus
  ) => (
    <Stack direction="row" alignItems="center" spacing={1}>
      <Tooltip title={roomType || "--"}>
        <Typography
          noWrap
          sx={{
            maxWidth: "20ch",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {roomType || "--"}
        </Typography>
      </Tooltip>
      {isMealIncluded !== undefined && (
        <Box
          component="img"
          sx={{ width: 20 }}
          src={isMealIncluded ? "/assets/Meal.svg" : "/assets/No-Meal.svg"}
          alt={isMealIncluded ? "Meal included" : "Meal not included"}
        />
      )}
      {/* <Typography>{`(${cancellationStatus || "NA"})`}</Typography> */}
    </Stack>
  );

  return (
    <TableContainer
      component={Paper}
      sx={{
        padding: "16px 8px",
      }}
    >
      <Table size="small">
        <TableHead>
          <TableRow>
            <TableCell>Hotel Name</TableCell>
            <TableCell align="right">Rate</TableCell>
            <TableCell align="center">Room Name</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {/* Hotel Data */}
          {calendarData[selectedDateIndex]?.hotelResult && (
            <TableRow>
              <TableCell>{currentHotel?.name ?? "--"}</TableCell>
              <TableCell align="right" sx={{ color: "#163A90" }}>
                {getPriceText(getHotelResult(), true)}
              </TableCell>
              <TableCell>
                {getHotelResult()
                  ? renderRoomType(
                      getHotelResult()?.roomType,
                      getHotelResult()?.isMealIncluded,
                      getHotelResult()?.conditionCode,
                      getHotelResult()?.cancellationStatus
                    )
                  : "(NA)"}
              </TableCell>
            </TableRow>
          )}

          {/* Competitors Data */}
          {competitors?.map((competitor, index) => {
            const competitorData = calendarData[
              selectedDateIndex
            ]?.competitorsResults?.find(
              ({ hotelId }) => hotelId === `${competitor.rateMetricHotelCode}`
            );

            if (!competitorData) return null;

            const lowestPriceItem = competitorData.sourceSortedArray?.reduce(
              (acc, curr) => (acc.price < curr.price ? acc : curr)
            );

            return (
              <TableRow key={index}>
                <TableCell>{competitor?.displayKey ?? "--"}</TableCell>
                <TableCell align="right">
                  {competitorData?.minPrice === -2
                    ? "(NA)"
                    : getPriceText(competitorData, false)}
                </TableCell>
                <TableCell>
                  {competitorData.internalStatus === "available"
                    ? renderRoomType(
                        lowestPriceItem?.roomType,
                        lowestPriceItem?.isMealIncluded,
                        lowestPriceItem?.conditionCode,
                        lowestPriceItem?.cancellationStatus
                      )
                    : "Sold-out"}
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};
