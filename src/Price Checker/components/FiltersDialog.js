import { Close } from "@mui/icons-material";
import {
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  FormLabel,
  IconButton,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  Stack,
  Tooltip,
  Typography,
} from "@mui/material";
import React, { useCallback } from "react";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { SelectHeader } from "../Styles";
import { KeyButton } from "../../AdminEvents/styles";
export const FilterDailog = ({
  open,
  onClose,
  loading,
  filters,
  setFilters,
  mealTypes,
  roomTypes,
  sourcesToShow,
  sources,
  nights,
  currentHotel,
  guests,
  optionsToShow = [],
  mobileChannels = [],
  channels = [],
  isMultipleChannel = false,
}) => {
  const history = useHistory();

  const searchParams = new URLSearchParams(history.location.search);

  const [newFilters, setNewFilters] = React.useState(filters);
  const handleApplyFilters = useCallback(async () => {
    setFilters(newFilters);

    searchParams.set("sources", newFilters?.channelIds?.[0]);
    searchParams.set("occupancy", newFilters?.guestName?.[0]?.[0]);
    searchParams.set("los", newFilters?.nightName?.[0]?.[0]);
    newFilters?.mealPlan?.[0] &&
      searchParams.set(
        "mealPlan",
        newFilters?.mealPlan?.[0]?.value ?? newFilters?.mealPlan?.[0]
      );
    newFilters?.roomType?.[0] &&
      searchParams.set(
        "roomType",
        newFilters?.roomType?.[0]?.value ?? newFilters?.roomType?.[0]
      );
    await history.replace({
      pathname: history.location.pathname,
      search: searchParams.toString(),
    });
    onClose();
  }, [history, newFilters, onClose, searchParams, setFilters]);

  const renderFields = [
    {
      label: "Channel",
      value: newFilters?.channelIds ?? [],
      isMultipleChannel,
      options: sourcesToShow,
      ...(isMultipleChannel
        ? {
            onChange: (e) => {
              const value = e.target.value;
              if (value.length === 0) return;
              if (value.includes("all")) return;

              const removeAll = value.filter((item) => item !== "all");
              setNewFilters((previous) => ({
                ...previous,
                channelIds: removeAll,
              }));
            },
            renderValue: (selected) => {
              if (!sourcesToShow) return "Loading...";
              if (sourcesToShow.length === 0) return "None";

              if (selected?.length === sourcesToShow.length) {
                return "All";
              }

              if (selected?.length > 1) {
                const firstSelectedChannel = sourcesToShow.find(
                  (channel) => channel?.sourceId === selected[0]
                );
                return `${firstSelectedChannel?.name}...`;
              }

              return selected.map((sourceId, index) => {
                const channel = sourcesToShow.find(
                  (channel) => channel?.sourceId === sourceId
                );
                return (
                  channel?.name + (selected.length - 1 === index ? " " : ", ")
                );
              });
            },
          }
        : {
            onChange: (e) => {
              const value = e.target.value;

              setNewFilters((previous) => ({
                ...previous,
                channelIds:
                  value === "All"
                    ? sourcesToShow?.map((channel) => channel.sourceId)
                    : [value],
              }));

              searchParams.set("sources", value);
            },
            renderValue: (selected) =>
              selected?.length === sourcesToShow?.length
                ? "All"
                : sourcesToShow?.find((ch) => ch?.sourceId === selected?.[0])
                    ?.name || "None",
          }),
    },

    {
      label: "Platform",
      value: newFilters?.sourceName,
      options: optionsToShow,
      onChange: (e) => {
        setNewFilters((previous) => ({
          ...previous,
          sourceName: [e.target.value],
          channelIds:
            e.target.value === sources[0]
              ? [channels[0]?.sourceId]
              : [mobileChannels[0]?.sourceId],
        }));
        const value =
          e.target.value === sources[0]
            ? channels[0]?.sourceId
            : mobileChannels[0]?.sourceId;

        searchParams.set("sources", value);
      },
    },

    {
      label: "LOS",
      value: newFilters?.nightName ?? [],
      options: nights,
      onChange: (e) => {
        setNewFilters((previous) => ({
          ...previous,
          nightName: [e.target.value],
        }));
        searchParams.set("los", e.target.value[0]);
      },
    },

    {
      label: "Guests",
      value: newFilters?.guestName ?? [],
      options: guests,
      onChange: (e) => {
        setNewFilters((previous) => ({
          ...previous,
          guestName: [e.target.value],
        }));
        searchParams.set("occupancy", e.target.value[0]);
      },
    },
    ...(currentHotel?.isRoomTypeMapped
      ? [
          {
            label: "Room Type",
            value: newFilters?.roomType?.[0],
            options: roomTypes,
            onChange: (e) => {
              setNewFilters((previous) => ({
                ...previous,
                roomType: [e.target.value],
              }));
              if (e.target.value !== "") {
                searchParams.set("roomType", e.target.value);
              } else {
                searchParams.delete("roomType");
              }
            },
            renderValue: (selected) => {
              let label = "Any Room";
              if (typeof selected === "string")
                label = roomTypes?.find((room) => room.value === selected)
                  ?.label;

              return (
                <Tooltip title="Room Type">
                  <Typography textTransform="capitalize">{label} </Typography>
                </Tooltip>
              );
            },
          },
        ]
      : []),
    ...(mealTypes?.length
      ? [
          {
            label: "Meal Type",
            value: newFilters?.mealPlan?.[0],
            options: mealTypes,
            onChange: (e) => {
              setNewFilters((previous) => ({
                ...previous,
                mealPlan: [e.target.value],
              }));
              if (e.target.value !== "") {
                searchParams.set("mealPlan", e.target.value);
              } else {
                searchParams.delete("mealPlan");
              }
            },
            renderValue: (selected) => {
              let label = "Any Meal";
              if (typeof selected === "string")
                label = mealTypes?.find((meal) => meal.value === selected)
                  ?.label;
              return <Tooltip title="Meal Plan"> {label} </Tooltip>;
            },
          },
        ]
      : []),
  ];
  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <IconButton
        sx={{
          position: "absolute",
          top: "8px",
          right: "10px",
        }}
        onClick={onClose}
      >
        <Close />
      </IconButton>

      <DialogContent>
        <Stack gap={2} mt={1} component="form">
          <FormLabel sx={{ textAlign: "center" }}>User Filters</FormLabel>
          <Stack
            flexDirection="row"
            flexWrap="wrap"
            sx={{
              justifyContent: "space-between",
              "@media (max-width: 429px)": {
                justifyContent: "center",
              },
            }}
            rowGap={2}
          >
            {renderFields?.map((filter, index) => (
              <RenderSelectField key={`filters-${index}`} {...filter} />
            ))}
          </Stack>
        </Stack>
      </DialogContent>
      <DialogActions
        sx={{
          justifyContent: "space-around",
          mb: "8px",
        }}
      >
        <Button
          onClick={() => {
            setNewFilters({
              channelIds: [],
              sourceName: [sources?.[0].toString()],
              nightName: [nights?.[0].toString()],
              guestName: [guests?.[0].toString()],
              exportName: "",
            });
          }}
        >
          Remove Filters
        </Button>
        <KeyButton onClick={handleApplyFilters}>
          {loading ? <CircularProgress /> : "Apply"}
        </KeyButton>
      </DialogActions>
    </Dialog>
  );
};

const RenderSelectField = ({
  label,
  value,
  options,
  onChange,
  renderValue,
  disableUnderline = true,
  isMultipleChannel,
}) => {
  const isChecked = (value, option) => {
    if (!value?.value && !value?.length) return false;
    if (typeof value === "string") {
      return value == option?.value;
    }
    if (isMultipleChannel) {
      return value?.includes(option?.sourceId ?? option);
    }
    if (value?.length) {
      return value?.[0] === (option?.value ?? option?.sourceId ?? option);
    }
    return value?.value === option?.value;
  };
  return (
    <SelectHeader size="small">
      <InputLabel>{label}</InputLabel>
      <Select
        variant="standard"
        label={label}
        value={value}
        multiple={isMultipleChannel ?? false}
        onChange={onChange}
        renderValue={renderValue || ((selected) => selected)}
        sx={{
          width: 150,
          paddingLeft: 2,
          "& .MuiSvgIcon-root": { color: "#306FBC", paddingBottom: "3px" },
        }}
        disableUnderline={disableUnderline}
        MenuProps={{ classes: { paper: "dropdownStyle" } }}
      >
        {options?.map((option) => (
          <MenuItem
            key={option?.value ?? option?.sourceId ?? option}
            value={option?.value ?? option?.sourceId ?? option}
          >
            <ListItemText primary={option?.label ?? option?.name ?? option} />
            <Checkbox
              sx={{ color: "#163A90" }}
              checked={isChecked(value, option)}
            />
          </MenuItem>
        ))}
      </Select>
    </SelectHeader>
  );
};
