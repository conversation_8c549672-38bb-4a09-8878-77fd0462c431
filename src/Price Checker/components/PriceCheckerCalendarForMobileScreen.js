import React, { useEffect, useState } from "react";
import {
  Cell,
  GridBox,
  Name,
  ResponsiveCalendar,
  ResponsiveCalendarTable,
  WeekResponsive,
} from "../Styles";
import { Button, CircularProgress, Container, Typography } from "@mui/material";
import { Stack } from "@mui/system";
import { PriceCheckerTableforDate } from "./PriceCheckertableFordate";
import moment from "moment";

const Days = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
export const PriceCheckerCalendarForMobileScreen = ({
  calendarData,
  getBackgroundColor,
  filters,
  currencies,
  currentHotel,
  channels,
  competitors,
  sourceImages,
  isLoading,
  oldestRate,
}) => {
  const [selectedDateIndex, setSelectedDateIndex] = useState();

  useEffect(() => {
    // index of first date in calendar data
    if (!calendarData?.length) return;
    setSelectedDateIndex(calendarData?.findIndex((i) => !!i));
  }, [calendarData]);
  if (isLoading) {
    return (
      <Container>
        <CircularProgress />
      </Container>
    );
  }
  return (
    <Container
      sx={{
        padding: 0,
        display: "flex",
        alignItems: "center",
        flexDirection: "column",
      }}
    >
      <ResponsiveCalendar>
        <WeekResponsive>
          {Days?.map((day, index) => (
            <Name
              key={`calender-${day}-column+${index}`}
              sx={{
                color: "black",
              }}
            >
              {day}
            </Name>
          ))}
        </WeekResponsive>
        <GridBox isMobileScreen={true}>
          {calendarData?.map((data, index) =>
            data !== "" ? (
              <Button
                key={index + data?.latestUpdated ?? data?.rateDate}
                onClick={() => setSelectedDateIndex(index)}
                variant="contained"
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: "50%",
                  minWidth: "auto",
                  border: selectedDateIndex === index ? "1px solid black" : "",
                  background:
                    data?.hotelResult?.minPrice === 0
                      ? "#ADB3AD"
                      : getBackgroundColor(
                          data?.hotelResult?.minPrice -
                            data?.meanCompetitorPrice,
                          data?.rateDate
                        ),
                }}
              >
                {data?.rateDate?.slice(8, 10)}
              </Button>
            ) : (
              <Cell></Cell>
            )
          )}
        </GridBox>
      </ResponsiveCalendar>

      <ResponsiveCalendarTable sx={{ paddingBottom: "60px" }}>
        <Stack flexDirection="column" gap={2}>
          <Typography fontWeight={700} textAlign="center">
            {selectedDateIndex
              ? new Date(calendarData[selectedDateIndex]?.rateDate)
                  .toLocaleString("en-IN", {
                    weekday: "long",
                    year: "numeric",
                    month: "short",
                    day: "numeric",
                  })
                  .replace(/,/g, "")
              : ""}
          </Typography>
          {!!oldestRate &&
            oldestRate !== "0001-01-01T00:00:00Z" &&
            calendarData?.[selectedDateIndex]?.hotelResult && (
              <Typography>
                Latest updated at{" "}
                {moment(
                  calendarData[selectedDateIndex]?.latestUpdated
                )?.fromNow()}{" "}
              </Typography>
            )}
          <Typography fontWeight={500}>
            {!!calendarData[selectedDateIndex]?.meanCompetitorPrice &&
              calendarData[selectedDateIndex]?.hotelResult?.internalStatus ===
                "available" && (
                <>
                  Average Comp Rate -{" "}
                  {calendarData[selectedDateIndex]?.meanCompetitorPrice === -2
                    ? "NA"
                    : (currencies?.[currentHotel.RSCurrency]?.symbol ??
                        currentHotel.RSCurrency) +
                      Math.round(
                        calendarData[selectedDateIndex]?.meanCompetitorPrice
                      )}
                </>
              )}
          </Typography>
        </Stack>
        <Stack maxWidth="95vw">
          {calendarData?.[selectedDateIndex]?.hotelResult ? (
            <PriceCheckerTableforDate
              calendarData={calendarData}
              channels={channels}
              competitors={competitors}
              currencies={currencies}
              currentHotel={currentHotel}
              filters={filters}
              selectedDateIndex={selectedDateIndex}
              sourceImages={sourceImages}
            />
          ) : (
            <Typography>No Data Available for selected date</Typography>
          )}
        </Stack>
      </ResponsiveCalendarTable>
    </Container>
  );
};
