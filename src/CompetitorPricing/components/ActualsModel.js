import React, { useState } from "react";
import { styled } from "@mui/system";
import { Box } from "@mui/material";
import {
  ChooseBtn,
  Cross,
  Done,
  Filename,
  Head,
  Section,
  Title,
  UploadSection,
} from "../Styles";
function getModalStyle() {
  const top = 8;
  const right = 4;

  return {
    top: `${top}%`,
    right: `${right}%`,
    transform: `translate(-${top}, -${right})`,
  };
}
const ClassWrapper = styled(Box)(({ theme }) => ({
  ".paper": {
    position: "absolute",
    width: 298,
    height: 240,
    backgroundColor: "#fff",
    boxShadow: 5,
  },
}));
export function ModelBody({
  fileHandler,
  fileGetter,
  setOpenSelectBar,
  filename,
  toggle,
  handleToggle,
}) {
  const [modalStyle] = useState(getModalStyle);
  return (
    <ClassWrapper>
      <Section style={modalStyle} className="paper">
        <Head>
          <Title>Upload Competitor Pricing</Title>
          <Cross onClick={() => setOpenSelectBar(false)}>&times;</Cross>
        </Head>

        <ChooseBtn
          style={toggle ? { display: "none" } : { display: "block" }}
          htmlFor="upload"
        >
          Choose File
          <input
            type="file"
            id="upload"
            onChange={(e) => fileGetter(e.target.files)}
            onClick={() => handleToggle()}
            style={{ display: "none" }}
          />
        </ChooseBtn>

        <UploadSection
          style={toggle ? { display: "flex" } : { display: "none" }}
        >
          <img
            src="/assets/excel.svg"
            alt="Excel Icon"
            width="36px"
            height="36px"
          />
          <Filename>{filename}</Filename>
        </UploadSection>
        <Done next onClick={fileHandler} type="button">
          Upload
        </Done>
      </Section>
    </ClassWrapper>
  );
}
