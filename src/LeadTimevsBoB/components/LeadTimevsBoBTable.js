import { Table, TableContainer, TableBody } from "@mui/material";
import React from "react";
import { getDateDDMM } from "../../sdk";
import {
  DATE,
  DAY,
  Tabhead,
  TCell as Td,
  TextOnly<PERSON>ooltip,
  THead,
  TRow,
} from "../Styles.js";

const Days = ["<PERSON>", "Mon", "<PERSON><PERSON>", "Wed", "Thu", "Fri", "Sat"];

export default function LeadTimevsBoBTable({ selectedData }) {
  const competitors = selectedData.find((data) => data.compSet);
  let competitorDetails;
  if (competitors) {
    let { compSet } = competitors;
    competitorDetails = compSet;
  }

  return (
    <TableContainer>
      <Table>
        <THead>
          <TRow>
            <Tabhead>As of</Tabhead>
            <Tabhead>Lead Time</Tabhead>
            <Tabhead>BoB</Tabhead>
            <Tabhead>Occupancy%</Tabhead>
            <Tabhead>Current Price</Tabhead>
            <Tabhead>System Price</Tabhead>
            <Tabhead>Override Price</Tabhead>
            <Tabhead>Price Difference</Tabhead>
            <Tabhead>Override Reason</Tabhead>
            {selectedData &&
              selectedData[0]?.compSet?.map((comp, index) => (
                <Tabhead key={index}>{comp.competitorName}</Tabhead>
              ))}
          </TRow>
        </THead>
        <TableBody>
          {selectedData.map((data, idx) => (
            <TRow
              key={idx}
              style={
                idx % 2 === 0
                  ? { backgroundColor: "rgba(48, 81, 221,0.09)" }
                  : {}
              }
            >
              <Td>
                <DATE style={{ alignItems: "center" }}>
                  <div>{getDateDDMM(data.date)}</div>
                  <DAY>{Days[new Date(data.date).getDay()]}</DAY>
                </DATE>
              </Td>
              <Td>{data.leadTime}</Td>
              <Td>{data.bob === -1 ? "-" : data.bob}</Td>
              <Td>
                {data.occupancyPercentage === -1
                  ? "-"
                  : `${parseFloat(data.occupancyPercentage).toFixed(2)}%`}
              </Td>
              <Td>
                {data.currentPrice === 0
                  ? "-"
                  : data.currentPrice === -1
                  ? "Sold Out"
                  : parseInt(data.currentPrice)}
              </Td>
              <Td>
                {data.systemPrice === 0 ? "-" : parseInt(data.systemPrice)}
              </Td>
              <Td>
                {data.overridePrice === 0 ? "-" : parseInt(data.overridePrice)}
              </Td>
              <Td>
                {data.overridePrice === 0 ? "-" : parseInt(data.difference)}
              </Td>
              <TextOnlyTooltip
                disableFocusListener
                disableTouchListener
                title={data.overrideReason}
                arrow
              >
                <Td>
                  {
                    <div
                      style={{
                        height: "30px",
                        maxWidth: "60px",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        lineHeight: "30px",
                        textAlign: "center",
                        whiteSpace: "nowrap",
                      }}
                    >
                      {data.overrideReason ? data.overrideReason : "-"}
                    </div>
                  }
                </Td>
              </TextOnlyTooltip>

              {data?.compSet?.map((comp, idx) => (
                <Td key={idx}>
                  {comp.price === -1
                    ? "Sold Out"
                    : comp.price === -999
                    ? "-"
                    : comp.price}
                </Td>
              ))}
            </TRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}
