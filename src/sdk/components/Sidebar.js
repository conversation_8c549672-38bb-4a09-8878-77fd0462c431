import { styled } from "@mui/system";
import { Tooltip, Box, Typography, ClickAwayListener } from "@mui/material";
import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Timeline,
  SwapVertOutlined,
  Settings,
  <PERSON><PERSON><PERSON>Rounded,
  LocalOffer,
  InsertChart,
  Event,
  EqualizerOutlined,
  Dashboard,
  Close,
  ChevronRight,
} from "@mui/icons-material";
import { useHistory } from "react-router-dom";
import { useWarning } from "../../Provider/context";
import { useAuth } from "../hooks";
import { PriceChecker } from "../../svgs/PriceChecker";
import { ChannelCompare } from "../../svgs/ChannelComparison";
import { UpcomingEvent } from "../../svgs/UpcomingEvent";
import { V2_ENABLED } from "../constants";
const Side = styled(Box)`
  width: ${(props) =>
    props.ismobilescreen ? "100%" : props.toggle ? "250px" : "60px"};
  height: ${(props) => (props.ismobilescreen ? "58px" : "100%")};
  box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16);
  background: #163a90 0% 0% no-repeat padding-box;
  position: fixed;
  bottom: 0px;
  left: 0px;
  &::-webkit-scrollbar {
    display: none;
  }
  z-index: 9999;
`;
const TextOnlyTooltip = styled(Tooltip)(({ theme }) => ({
  ".tooltip": {
    color: "#ffffff",
    font: "normal normal bold 16px/20px Roboto",
    boxShadow: "0px 4px 8px #00000035",
    background: "#163A90 !important",
    minWidth: 200,
    textAlign: "left",
  },
  ".arrow": {
    "&:before": {
      border: "1px solid #E6E8ED",
    },
    color: "#163A90 !important",
  },
}));

const LogoHeader = styled(Box)`
  display: flex;
  padding: 22px 24px 0px 24px;
  width: 100%;
`;

const Menu = styled(Box)`
  display: flex;
  flex-direction: ${(props) => (props.ismobilescreen ? "row" : "column")};
  justify-content: space-between;
  align-items: ${(props) => (props.ismobilescreen ? "center" : "flex-start")};
  width: 100%;
  height: 92%;
`;
const P = styled(Typography)`
  font: normal normal normal 16px/20px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  padding: 0px 24px;

  display: flex;
  align-items: center;
`;
const grayBackgroundStyle = {
  backgroundColor: "grey",
};
const SideLink = (props) => {
  const { handleRedirect } = useWarning();
  const {
    children,
    to,
    activeStyle,
    onClick,
    showGrayBackground = false,
    isSidebarOpen,
  } = props;

  const BackgroundStyle = activeStyle?.backgroundColor
    ? activeStyle
    : showGrayBackground
    ? grayBackgroundStyle
    : {};
  return (
    <Box
      onClick={() => {
        handleRedirect(to);
        onClick();
      }}
      sx={{
        textDecoration: "none",
        width: "100%",
        paddingLeft: isSidebarOpen ? "8px" : "16px",
        height: "40px",
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-start",
        color: "white",
        cursor: "pointer",
        // marginRight: "18px",
        ...BackgroundStyle,
      }}
    >
      {children}
    </Box>
  );
};

// const TextNoWrap = styled(Typography)`
//   white-space: nowrap;
// `;

const SubMenuLink = (props) => {
  const { handleRedirect } = useWarning();
  const {
    children,
    to,
    activeStyle,
    style,
    onClick,
    showGrayBackground,
  } = props;
  const BackgroundStyle = activeStyle?.backgroundColor
    ? activeStyle
    : showGrayBackground
    ? grayBackgroundStyle
    : {};
  return (
    <Box
      id="menu-item"
      onClick={(e) => {
        handleRedirect(to);
        onClick(e);
      }}
      sx={{
        textDecoration: "none",
        width: "100%",
        height: "32px",
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-start",
        color: "white",
        fontWeight: "normal",
        padding: "0px 5px",
        cursor: "pointer",
        ...style,
        ...BackgroundStyle,
        "&:hover": {
          backgroundColor: "grey !important",
        },
      }}
    >
      {children}
    </Box>
  );
};

const LinkAndIcon = styled(Box)`
  display: flex;
  width: 100%;
  justify-content: flex-start;
  align-items: center;
  color: white;
`;
const IconsSidebar = styled(Box)`
  height: 100%;
  background: #163a90 0% 0% no-repeat padding-box;
  width: ${(props) =>
    props.toggle ? "250px" : props.ismobilescreen ? "95vw" : "60px"};
  margin-top: ${(props) => (props.ismobilescreen ? "0" : "30px")};
  color: white;
`;
const MenuIcons = styled(Box)`
  height: ${(props) => (props.ismobilescreen ? "fit-content" : "450px")};
  display: flex;
  flex-direction: ${(props) => (props.ismobilescreen ? "row" : "column")};
  justify-content: ${(props) =>
    props.ismobilescreen ? "center" : "flex-start"};
  align-items: center;
  width: ${(props) => (props.ismobilescreen ? "96vw" : "100%")};
  overflow: ${(props) => (props.ismobilescreen ? "auto" : "visible")};
`;
const Precium = styled(Typography)`
  font: normal normal normal 24px/24px Cochin;
  letter-spacing: 0px;
  color: #ffffff;
`;

const Logo = styled(Box)`
  width: 18px;
  height: 24px;
  margin: 0 8px 0 0;
  &:hover {
    cursor: pointer;
  }
`;

const OpenNCloseMenu = styled(Box)`
  margin: 80px auto 0px auto;
  display: flex;
  justify-content: ${(props) => (props.toggle ? "flex-end" : "center")};
  align-items: center;
  &:hover {
    cursor: pointer;
  }
  width: 100%;
  padding-right: ${(props) => (props.toggle ? "20px" : "0px")};
`;
const SideBar = styled(Box)`
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: ${(props) => (props.ismobilescreen ? "row" : "column")};
  justify-content: space-between;
  align-items: center;
`;

const SelectedMenu = styled(Box)`
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  padding-left: 8px;
`;

const BottomSection = styled(Box)`
  display: flex;
`;

const RenderSidebarIcons = ({
  showGrayBackground,
  navigateTo,
  isActive,
  isSubmenu,
  iconStyle,
  isSidebarOpen,
  onClick,
  Icon,
  label,
  linkText,
  forecastSelected,
  selected,
}) => {
  if (isSubmenu && isSidebarOpen) {
    return (
      <SelectedMenu
        style={{
          backgroundColor: isActive ? "white" : "inherit",
        }}
      >
        <TextOnlyTooltip
          disableFocusListener
          interactive
          title={label}
          placement="right"
        >
          <LinkAndIcon sx={{ color: isActive ? "#163a90" : "" }}>
            {Icon}
            {isSidebarOpen && typeof linkText === "string" ? (
              <P sx={{ color: isActive ? "#163a90" : "" }}>{linkText}</P>
            ) : (
              linkText
            )}
          </LinkAndIcon>
        </TextOnlyTooltip>
      </SelectedMenu>
    );
  }
  return (
    <SideLink
      showGrayBackground={showGrayBackground}
      exact
      to={navigateTo}
      isSidebarOpen={isSidebarOpen}
      activeStyle={
        isActive
          ? {
              textDecoration: "none",
              backgroundColor: "white",
            }
          : {}
      }
      onClick={onClick}
    >
      <LinkAndIcon sx={{ color: isActive ? "#163a90" : "" }}>
        {Icon}
        {isSidebarOpen ? (
          typeof linkText === "string" ? (
            <P sx={{ color: isActive ? "#163a90" : "" }}>{linkText}</P>
          ) : (
            linkText
          )
        ) : null}
      </LinkAndIcon>
    </SideLink>
  );
};
export const Sidebar = ({ isSidebarOpen, handleSidebar, isMobileScreen }) => {
  const history = useHistory();
  const {
    hotelSelect,
    selectHotel,
    permission,
    version,
    rateShopFeature,
    state,
    currentHotel,
  } = useAuth();

  const hotelId = hotelSelect?.id || null;
  const { formEdit, setformEdit, handleRedirect } = useWarning();
  const [selected, setSelected] = useState(
    window.location.pathname.indexOf("analytics") > -1
  );
  const [forecastSelected, setForecastSelected] = useState(
    window.location.pathname.indexOf("forecast") > -1
  );
  useEffect(() => {
    if (window.location.pathname.indexOf("forecast") > -1) {
      setSelected(false);
      setForecastSelected(true);
    }
    if (window.location.pathname.indexOf("analytics") > -1) {
      setForecastSelected(false);
      setSelected(true);
    }
    if (
      window.location.pathname.indexOf("forecast") === -1 &&
      window.location.pathname.indexOf("analytics") === -1
    ) {
      setSelected(false);
      setForecastSelected(false);
    }
  }, [window.location.pathname]);
  const wrapperRef = useRef(null);
  const [open, setOpen] = React.useState(false);

  const handleTooltipClose = () => {
    setOpen(false);
  };
  const handleTooltipOpen = () => {
    setOpen(true);
  };

  let dashboard = null,
    forecastView = null,
    compPriceView = null,
    PUReport = null,
    analytics = null,
    pastDataView = null,
    priceRecommendationWithoutCalc = null,
    eventsCalendarView = null,
    setupView = null,
    showRateshop = null,
    viewRateParity = null,
    viewUpcomingEvents = null;

  const isPreciumEnabled = useMemo(
    () => currentHotel?.isPreciumEnabled ?? true,
    [currentHotel?.isPreciumEnabled]
  );

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (
        wrapperRef &&
        wrapperRef.current &&
        !wrapperRef.current.contains(e.target)
      ) {
        if (isSidebarOpen) {
          handleSidebar();
        }
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => {
      if (document) {
        document.removeEventListener("click", handleClickOutside);
      }
    };
  }, [wrapperRef.current, isSidebarOpen]);

  for (let key in permission) {
    if (permission.hasOwnProperty(key)) {
      if (permission[key].name === "dashboard") {
        dashboard = permission[key];
      }
      if (permission[key].name === "forecastView") {
        forecastView = permission[key];
      }
      if (permission[key].name === "compPriceView") {
        compPriceView = permission[key];
      }
      if (permission[key].name === "PUReport") {
        PUReport = permission[key];
      }
      if (permission[key].name === "analytics") {
        analytics = permission[key];
      }
      if (permission[key].name === "pastDataView") {
        pastDataView = permission[key];
      }
      if (permission[key].name === "priceRecommendationWithoutCalc") {
        priceRecommendationWithoutCalc = permission[key];
      }
      if (permission[key].name === "eventsCalendarView") {
        eventsCalendarView = permission[key];
      }
      if (permission[key].name === "hotelDetailsView") {
        setupView = permission[key];
      }
      if (permission[key].name === "showRateshop") {
        showRateshop = permission[key];
      }
      if (permission[key].name === "viewUpcomingEvents") {
        viewUpcomingEvents = permission[key];
      }
      if (permission[key].name === "viewRateParity") {
        viewRateParity = permission[key];
      }
    }
  }

  const menuIcons = useMemo(
    () => [
      {
        label: "Dashboard",
        linkText: "Dashboard",
        icon: <Dashboard />,
        redirectTo: `/hotel/${hotelId}/dashboard`,
        isActive: history.location.pathname.includes("/dashboard"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
          handleRedirect(`/hotel/${hotelId}/dashboard`);
        },
        showGrayBackground: !isPreciumEnabled,
        showIcon: dashboard && !isMobileScreen,
      },
      {
        label: "Business on Books",
        linkText: "B.O.B Upload",
        icon: <Timeline />,
        redirectTo: `/hotel/${hotelId}/business_on_books`,
        isActive: history.location.pathname.includes("/business_on_books"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showGrayBackground: !isPreciumEnabled,
        showIcon: forecastView,
      },
      {
        label: "Rate Comparison",
        linkText: "Rate Comparison",
        icon: <EqualizerOutlined />,
        redirectTo: `/hotel/${hotelId}/rate_comparison`,
        isActive: history.location.pathname.includes("/rate_comparison"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showGrayBackground: !isPreciumEnabled,
        showIcon: compPriceView,
      },
      {
        label: "Pick-Up Report",
        linkText: "Pick-Up Report",
        icon: <SwapVertOutlined />,
        redirectTo: `/hotel/${hotelId}/pickup_report`,
        isActive: history.location.pathname.includes("/pickup_report"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showGrayBackground: !isPreciumEnabled,
        showIcon: PUReport,
      },
      {
        label: " Pricing Recommendation",
        linkText: (
          <P
            sx={{
              color: history.location.pathname.includes("/price_recommendation")
                ? "#163a90"
                : "",
            }}
          >
            Pricing&nbsp;Recommendation
          </P>
        ),
        icon: <LocalOffer />,
        redirectTo: `/hotel/${hotelId}/price_recommendation`,
        isActive: history.location.pathname.includes("/price_recommendation"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showGrayBackground: !isPreciumEnabled,
        showIcon: priceRecommendationWithoutCalc,
      },
      {
        label: "Events Calendar",
        linkText: (
          <P
            sx={{
              color: history.location.pathname.includes("/calendar")
                ? "#163a90"
                : "",
            }}
          >
            Events&nbsp;Calendar
          </P>
        ),
        icon: <Event />,
        redirectTo: `/hotel/${hotelId}/new_event/calendar`,
        isActive: history.location.pathname.includes("/calendar"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showGrayBackground: !isPreciumEnabled,
        showIcon: eventsCalendarView,
      },
      {
        label: "Upcoming Events",
        linkText: "Upcoming Events",
        icon: <UpcomingEvent  color={history.location.pathname.includes("/event-details") ? "#163a90":"white"}/>,
        redirectTo: `/hotel/${hotelId}/event-details`,
        isActive: history.location.pathname.includes("/event-details"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showIcon: currentHotel?.canViewUpcomingEvents && viewUpcomingEvents,
      },
      {
        linkText: "Analytics",
        label: (
          <div style={{ width: "200px" }}>
            <SubMenuLink
              showGrayBackground={!isPreciumEnabled}
              style={{
                fontWeight: "bold",
                borderBottom: "0.5px solid white",
              }}
              onClick={(e) => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
            >
              Analytics
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/analytics/pace_analysis`}
              onClick={() => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
              activeStyle={
                history.location.pathname.includes("/pace_analysis")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
            >
              Pace Analysis
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/analytics/dow_analysis`}
              activeStyle={
                history.location.pathname.includes("/dow_analysis")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
            >
              DOW Analysis
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/analytics/lead_time_vs_bob_daily`}
              activeStyle={
                history.location.pathname.includes("/lead_time_vs_bob_daily")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
            >
              Lead Time - BoB Daily
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/analytics/lead_time_vs_bob_dow`}
              activeStyle={
                history.location.pathname.includes("/lead_time_vs_bob_dow")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
            >
              Lead Time - BoB Monthly
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/analytics/lead_time_vs_pickup_daily`}
              activeStyle={
                history.location.pathname.includes("/lead_time_vs_pickup_daily")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
            >
              Lead Time - Pick-Up Daily
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/analytics/lead_time_vs_pickup_dow`}
              activeStyle={
                history.location.pathname.includes("/lead_time_vs_pickup_dow")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
            >
              Lead Time - Pick-Up Monthly
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/analytics/dow_pickup_performance`}
              activeStyle={
                history.location.pathname.includes("/dow_pickup_performance")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(true);
                setForecastSelected(false);
                handleTooltipClose();
              }}
            >
              DoW Pick-Up Performance
            </SubMenuLink>
          </div>
        ),
        icon: <PieChartRounded />,
        redirectTo: `/hotel/${hotelId}/analytics/pace_analysis`,
        type: "submenu",
        isActive: history.location.pathname.includes("/analytics"),
        showIcon: analytics,
        onClick: () => {
          setSelected(true);
          setForecastSelected(false);
          handleTooltipClose();
        },
        showGrayBackground: !isPreciumEnabled,
      },
      {
        label: "Past Data",
        linkText: "Past Data",
        icon: <InsertChart />,
        redirectTo: `/hotel/${hotelId}/pastdata`,
        isActive: history.location.pathname
          ?.split("/")[3]
          ?.includes("pastdata"),
        showIcon: pastDataView,
        showGrayBackground: !isPreciumEnabled,
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
      },
      {
        linkText: "Budget Forecast",
        label: (
          <div>
            <SubMenuLink
              to={`/hotel/${hotelId}/forecast/forecast_sheet_daily`}
              style={{
                fontWeight: "bold",
                borderBottom: "0.5px solid white",
              }}
              activeStyle={{
                textDecoration: "none",
              }}
              onClick={() => {
                setSelected(false);
                setForecastSelected(true);
              }}
            >
              Budget Forecast
            </SubMenuLink>

            <SubMenuLink
              to={`/hotel/${hotelId}/forecast/pickup`}
              activeStyle={
                history.location.pathname.includes("/pickup")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(false);
                setForecastSelected(true);
              }}
            >
              PickUp
            </SubMenuLink>

            <SubMenuLink
              to={`/hotel/${hotelId}/forecast/forecast_sheet_upload`}
              activeStyle={
                history.location.pathname.includes("/forecast_sheet_upload")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(false);
                setForecastSelected(true);
              }}
            >
              {/* Budget Forecast Upload */}
              BOB Upload
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/forecast/forecast_sheet_daily`}
              activeStyle={
                history.location.pathname.includes("/forecast_sheet_daily")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(false);
                setForecastSelected(true);
              }}
            >
              {/* Budget Forecast Daily */}
              Forecast Daily
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/forecast/forecast_sheet_monthly`}
              activeStyle={
                history.location.pathname.includes("/forecast_sheet_monthly")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(false);
                setForecastSelected(true);
              }}
            >
              Forecast Monthly
            </SubMenuLink>
            <SubMenuLink
              to={`/hotel/${hotelId}/forecast/pastdata`}
              activeStyle={
                history.location.pathname.includes("/pastdata")
                  ? {
                      textDecoration: "none",
                      backgroundColor: "grey",
                    }
                  : {}
              }
              onClick={() => {
                setSelected(false);
                setForecastSelected(true);
              }}
            >
              Past Data(Market Segment)
            </SubMenuLink>
          </div>
        ),
        type: "submenu",
        icon: (
          <img src="/assets/ForecastIcon.svg" alt="forecast" width="24px" />
        ),
        iconStyle: {
          paddingLeft: "16px",
          height: "40px",
          cursor: "pointer",
          backgroundColor: forecastSelected ? "grey" : "inherit",
        },
        onClick: () => {
          setSelected(false);
          setForecastSelected(true);
        },
        // redirectTo: `/hotel/${hotelId}/forecast/forecast_sheet_daily`,
        isActive: history.location.pathname.includes("/forecast"),
        showIcon: version === "v2",
      },
      {
        label: "Rate Shppping",
        linkText: "Rate Shopping",
        icon: (
          <PriceChecker
            color={
              history.location.pathname.includes("/price-checker")
                ? "#163a90"
                : "white"
            }
          />
        ),
        redirectTo: `/hotel/${hotelId}/price-checker`,
        isActive: history.location.pathname.includes("/price-checker"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showIcon: rateShopFeature && showRateshop,
      },
      {
        linkText: "Rate Parity",
        label: "Rate Parity",
        icon: (
          <ChannelCompare
            color={
              history.location.pathname.includes("/channel-comparison")
                ? "#163a90"
                : "white"
            }
          />
        ),
        redirectTo: `/hotel/${hotelId}/channel-comparison`,
        isActive: history.location.pathname.includes("/channel-comparison"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showIcon: rateShopFeature && viewRateParity,
      },
      {
        linkText: "Setup",
        label: "Setup",
        icon: <Settings />,
        redirectTo: `/hotel/${hotelId}/setup`,
        isActive: history.location.pathname.includes("/setup"),
        onClick: () => {
          setSelected(false);
          setForecastSelected(false);
        },
        showIcon: setupView,
      },
    ],
    [
      PUReport,
      analytics,
      compPriceView,
      currentHotel?.canViewUpcomingEvents,
      dashboard,
      eventsCalendarView,
      forecastSelected,
      forecastView,
      handleRedirect,
      history.location.pathname,
      hotelId,
      isMobileScreen,
      isPreciumEnabled,
      pastDataView,
      priceRecommendationWithoutCalc,
      rateShopFeature,
      setupView,
      showRateshop,
      version,
      viewRateParity,
      viewUpcomingEvents,
    ]
  );

  return hotelSelect ? (
    <Side
      ref={wrapperRef}
      toggle={isSidebarOpen}
      ismobilescreen={isMobileScreen}
    >
      {!isMobileScreen && (
        <LogoHeader>
          <Logo
            onClick={() => {
              selectHotel(null);
            }}
          >
            <img
              src="/assets/logo-white.svg"
              alt="Logo"
              width="18px"
              height="24px"
            />
          </Logo>
          {isSidebarOpen && <Precium>Precium</Precium>}
        </LogoHeader>
      )}

      <Menu ismobilescreen={isMobileScreen}>
        <IconsSidebar toggle={isSidebarOpen} ismobilescreen={isMobileScreen}>
          <SideBar ismobilescreen={isMobileScreen}>
            {isSidebarOpen ? (
              <MenuIcons>
                {menuIcons.map(
                  (item, index) =>
                    item?.showIcon && (
                      <RenderSidebarIcons
                        selected={selected}
                        key={`${index}-${
                          typeof item.label === "string"
                            ? item.label
                            : item.type
                        }`}
                        isSidebarOpen={isSidebarOpen}
                        Icon={item.icon}
                        isSubmenu={item.type === "submenu"}
                        forecastSelected={forecastSelected}
                        isActive={item?.isActive}
                        showGrayBackground={item?.showGrayBackground ?? false}
                        navigateTo={item?.redirectTo}
                        linkText={item?.linkText ?? ""}
                        onClick={item?.onClick}
                        iconStyle={item?.iconStyle}
                        label={item?.label}
                      />
                    )
                )}
              </MenuIcons>
            ) : (
              <MenuIcons ismobilescreen={isMobileScreen}>
                {menuIcons.map(
                  (item, index) =>
                    item?.showIcon && (
                      <TextOnlyTooltip
                        key={`${index}-${
                          typeof item.label === "string"
                            ? item.label
                            : item.type
                        }`}
                        disableFocusListener
                        sx={{
                          width: "100%",
                        }}
                        title={item?.label}
                        placement="right-end"
                        arrow
                      >
                        <div>
                          <RenderSidebarIcons
                            isSidebarOpen={isSidebarOpen}
                            Icon={item.icon}
                            isSubmenu={item.type === "submenu"}
                            isActive={item?.isActive}
                            showGrayBackground={
                              item?.showGrayBackground ?? false
                            }
                            navigateTo={item?.redirectTo}
                            linkText={item?.linkText ?? item?.label}
                            onClick={item?.onClick}
                            iconStyle={item?.iconStyle}
                            label={item?.label}
                          />
                        </div>
                      </TextOnlyTooltip>
                    )
                )}
              </MenuIcons>
            )}
            {!isMobileScreen && (
              <OpenNCloseMenu toggle={isSidebarOpen}>
                <Logo
                  onClick={() => {
                    handleSidebar();
                  }}
                >
                  <BottomSection>
                    <Close
                      style={{
                        display: !isSidebarOpen ? "none" : "block",
                      }}
                    />
                    <img
                      src="/assets/menu.svg"
                      alt="menu"
                      width="24px"
                      height="24px"
                      style={{
                        display: isSidebarOpen ? "none" : "block",
                      }}
                    />
                  </BottomSection>
                </Logo>
              </OpenNCloseMenu>
            )}
          </SideBar>
        </IconsSidebar>
      </Menu>
    </Side>
  ) : (
    <div />
  );
};
