import React from "react";
import { styled } from "@mui/system";

const InputComp = styled("input")`
  width: ${(props) => (props.type === "number" ? "32px" : "90px")};
  height: 20px;
  box-sizing: border-box;
  font-family: Roboto;
  font-size: 16px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.25;
  letter-spacing: normal;
  text-align: left;
  color: #333333;
  background: none;
  border: none;
`;

export const TableInput = (props) => {
  return <InputComp {...props} />;
};
