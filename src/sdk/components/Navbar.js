import {
  Box,
  TextField as TF,
  Typography,
  AppBar as AB,
  Container,
  <PERSON>ack,
  IconButton,
} from "@mui/material";
import { styled, useMediaQuery } from "@mui/system";
import { Edit as ED, Forum, List, People, Search } from "@mui/icons-material";
import ManageAccountsIcon from "@mui/icons-material/ManageAccounts";
import Autocomplete from "@mui/material/Autocomplete";
import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { Link, NavLink } from "react-router-dom";
import { useAuth } from "../hooks";
import { Sidebar } from "./Sidebar";
const TextField = styled(TF)`
  &.MuiFormControl-root {
    width: ${(props) => (props.ismobilescreen ? "150px" : "300px")};
  }
  input {
    width: 100%;
    color: #fff;
    text-align: center;
    font: normal normal bold 18px Roboto;
    display: inline-block;
    padding: 0 0 0 20px;
  }
`;

const LogoContainer = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 8px;
  width: ${(props) => (props.toggle ? "260px" : "60px")};
`;
const Logo = styled(Box)`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  &:hover {
    cursor: pointer;
  }
`;
const Title = styled(Typography)`
  text-align: left;
  font: normal normal normal 25px/30px Cormorant Garamond;
  color: #ffffff;
  padding-left: 4px;
`;
const Nav = styled(AB)`
  display: flex;
  background: #163a90 0% 0% no-repeat padding-box;
  width: 100%;
  height: 60px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
`;
const HotelName = styled(Box)`
  // width: 100%;
  display: flex;
  justify-content: center;
  text-align: center;
  background-color: #0f2662;
  border-radius: 10px;
  height: 36px;
`;
const LogoutIconNew = styled(Box)`
  margin-top: auto;
  margin-bottom: auto;
  width: 28px;
  border-radius: 50%;
  height: 28px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    cursor: pointer;
  }
  margin-left: 14px;
`;
const UserIcon = styled(Box)`
  padding: 4px;
  background: #112d75 0% 0% no-repeat padding-box;
  width: 40px;
  border-radius: 50%;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    cursor: pointer;
  }
  margin-right: 8px;
  margin-left: 16px;
`;
const Edit = styled(Box)`
  padding: 4px;
  background: #112d75 0% 0% no-repeat padding-box;
  width: 40px;
  border-radius: 50%;
  height: 36px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    cursor: pointer;
  }
  margin-right: 16px;
`;
const Header = styled(Typography)`
  font: normal normal bold 24px/30px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
  padding-right: 8px;
`;
export const Navbar = ({ refreshNavbar, pageHeader }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [differentOrganization, setDifferentOrganization] = useState(false);
  const isMobileScreen = useMediaQuery("(max-width:600px)");
  const [showSearchHotelDropDown, setShowSearchHotelDropDown] = useState(false);

  const {
    isLoggedIn,
    selectHotel,
    hotelSelect,
    hotels,
    logout,
    permission,
    token,
    authFetch,
    name,
  } = useAuth();
  const hotelId = hotelSelect?.id || null;
  let listOfOrg = null,
    userRightsView = null,
    dailyLogs = null,
    monthlyLogs = null,
    editPermission = null;
  let broadcastMessage = null;

  useEffect(() => {
    if (!token) {
      return;
    }
    if (token && hotels) {
      hotels.forEach((_, index) => {
        if (
          hotels[index + 1] &&
          hotels[index].organizationId !== hotels[index + 1].organizationId
        ) {
          setDifferentOrganization(true);
        } else {
          setDifferentOrganization(false);
        }
      });
    }
  }, [token, hotels]);
  for (let key in permission) {
    if (permission.hasOwnProperty(key)) {
      if (permission[key].name === "listOfOrg") {
        listOfOrg = permission[key];
      }
      if (permission[key].name === "userRightsView") {
        userRightsView = permission[key];
      }
      if (permission[key].name === "monthlyLogs") {
        monthlyLogs = permission[key];
      }
      if (permission[key].name === "dailyLogs") {
        dailyLogs = permission[key];
      }
      if (permission[key].name === "editPermission") {
        editPermission = permission[key];
      }
    }
  }
  const handleSidebar = useCallback(() => {
    setIsSidebarOpen(!isSidebarOpen);
  }, [isSidebarOpen]);

  const handleChange = useCallback(
    (event, { name: hotelname }) => {
      const foundHotel = hotels.find((hotel) => hotel.name === hotelname);
      if (foundHotel) {
        selectHotel(foundHotel);
      } else {
        selectHotel(hotelSelect);
        window.location.reload();
        return;
      }
      const orgid = hotels.find((hotel) => hotel.name === hotelname)
        .organizationId;
      if (orgid) {
        localStorage.setItem("organizationId", JSON.stringify());
      }
      window.location.reload();
    },
    [hotels, selectHotel, hotelSelect]
  );

  const logoutHandler = () => {
    logout();
  };
  const defaultHotelProps = {
    options: [
      ...hotels
        ?.map((hotel) => hotel)
        .sort((a, b) => a.name.localeCompare(b.name)),
    ],
    getOptionLabel: (option) => option.name ?? option,
    getOptionKey: (option) => option.id,
  };

  const sortHotels = useCallback((a, b, inputVal) => {
    if (!a || !b || !inputVal) return 0;
    const pos1 = a?.name.toLowerCase().indexOf(inputVal.toLowerCase());
    const pos2 = b?.name.toLowerCase().indexOf(inputVal.toLowerCase());

    if (pos1 < pos2) return -1;
    else if (pos1 > pos2) return 1;
    else a.name.localeCompare(b.name);
  }, []);
  const searchedHotels = useCallback(
    (options, inputValue) => {
      const filteredHotels = options.filter((option) =>
        option.name.toLowerCase().includes(inputValue.toLowerCase())
      );

      return filteredHotels.sort((a, b) => sortHotels(a, b, inputValue));
    },
    [sortHotels]
  );
  const RenderSearchAutoComplete = memo(() => {
    if (
      hotelSelect &&
      window.location.pathname.split("/")[1] === "hotel" &&
      window.location.pathname.split("/")[3] !== "users"
    )
      return (
        <HotelName>
          <Autocomplete
            {...defaultHotelProps}
            value={hotelSelect.name.trim()}
            freeSolo={true}
            disableClearable
            onChange={(event, newValue) => {
              handleChange(event.target, newValue);
            }}
            filterOptions={(option, { inputValue }) => {
              return searchedHotels(option, inputValue);
            }}
            renderInput={(params) => (
              <TextField
                ismobilescreen={isMobileScreen}
                {...params}
                variant="standard"
                placeholder="Search Hotel"
                InputProps={{
                  ...params.InputProps,
                  disableUnderline: true,
                }}
              />
            )}
          />
        </HotelName>
      );
    return null;
  }, [defaultHotelProps, handleChange, hotelSelect, searchedHotels]);

  const RenderNavButtons = () => {
    return (
      <Container
        style={{
          display: "flex",
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          margin: 0,
          maxWidth: "100%",
        }}
      >
        <Typography
          flex={1}
          textAlign={
            window.location.pathname.split("/")[1] === "hotel" &&
            window.location.pathname !== "/hotel"
              ? "left"
              : "center"
          }
          fontWeight={500}
          width="100%"
        >
          Hello {name?.split(" ")?.[0]}, Welcome to Precium
        </Typography>
        <Stack direction={"row"} alignItems="center" justifyContent="flex-end">
          {(dailyLogs || monthlyLogs) &&
          hotelSelect &&
          window.location.pathname.split("/")[1] === "hotel" ? (
            <UserIcon
              sx={{
                marginLeft: "8px",
              }}
            >
              <Link
                style={{ display: "flex", justifyContent: "center" }}
                exact="true"
                to={`/hotel/${hotelId}/data_upload_information`}
              >
                <List style={{ color: "white" }} />
              </Link>
            </UserIcon>
          ) : (
            ""
          )}
          {window.location.pathname.split("/")[1] === "hotel" ? (
            <Edit
              sx={{
                ml: "8px",
              }}
            >
              <Link
                style={{ display: "flex", justifyContent: "center" }}
                exact="true"
                to={`/hotel/${hotelId}/update-profile`}
              >
                <ED style={{ color: "white" }} />
              </Link>
            </Edit>
          ) : (
            ""
          )}
          <RenderSearchAutoComplete />
          {userRightsView && hotelSelect && editPermission && (
            <UserIcon>
              <Link exact="true" to={`/hotel/${hotelId}/role-management`}>
                {" "}
                <ManageAccountsIcon sx={{ color: "white" }} />
              </Link>
            </UserIcon>
          )}
          {userRightsView &&
            hotelSelect &&
            window.location.pathname.split("/")[1] === "hotel" && (
              <UserIcon>
                <Link exact="true" to={`/hotel/${hotelId}/users`}>
                  {" "}
                  <People style={{ color: "white" }} />
                </Link>
              </UserIcon>
            )}{" "}
          <LogoutIconNew>
            <img
              src="/assets/NewLogoutIcon.svg"
              alt="logout icon"
              onClick={logoutHandler}
              style={{
                color: "white",
                width: "30px",
                height: "30px",
              }}
            />
          </LogoutIconNew>
        </Stack>
      </Container>
    );
  };
  const RenderResponsiveNavHeaderButtons = memo(() => {
    if (!isMobileScreen) return <RenderNavButtons />;
    return (
      <Container
        style={{
          display: "flex",
          justifyContent: "flex-end",
        }}
      >
        <Stack flexDirection="row" px={3}>
          {!showSearchHotelDropDown ? (
            <IconButton onClick={() => setShowSearchHotelDropDown(true)}>
              <Search
                sx={{
                  color: "white",
                  width: "30px",
                  height: "30px",
                }}
              />
            </IconButton>
          ) : (
            <RenderSearchAutoComplete />
          )}
          <LogoutIconNew>
            <img
              src="/assets/NewLogoutIcon.svg"
              alt="logout icon"
              onClick={logoutHandler}
              style={{
                color: "white",
                width: "30px",
                height: "30px",
              }}
            />
          </LogoutIconNew>
        </Stack>
      </Container>
    );
  }, [isMobileScreen]);

  return (
    <>
      {isLoggedIn ? (
        <>
          <Nav>
            <Stack width={"100%"} direction={"row"} alignItems={"center"}>
              {hotelSelect &&
              window.location.pathname.split("/")[1] === "hotel" ? (
                <div style={{ display: "flex", alignItems: "center" }}>
                  <LogoContainer toggle={isSidebarOpen}>
                    <Logo>
                      <img
                        src="/assets/logo-white.svg"
                        alt="Logo"
                        width="18px"
                        height="24px"
                      />
                    </Logo>
                  </LogoContainer>
                  <Header>{pageHeader}</Header>
                </div>
              ) : listOfOrg ? (
                <NavLink
                  to="/managing_organization"
                  style={{ textDecoration: "none" }}
                >
                  <LogoContainer toggle={isSidebarOpen}>
                    <Logo>
                      <img
                        src="/assets/logo-white.svg"
                        alt="Logo"
                        width="18px"
                        height="24px"
                      />
                    </Logo>
                    <Title>Precium</Title>
                  </LogoContainer>
                </NavLink>
              ) : (
                <NavLink to="/choose_hotel" style={{ textDecoration: "none" }}>
                  <LogoContainer toggle={isSidebarOpen}>
                    <Logo>
                      <img
                        src="/assets/logo-white.svg"
                        alt="Logo"
                        width="18px"
                        height="24px"
                      />
                    </Logo>
                    <Title>Precium</Title>
                  </LogoContainer>
                </NavLink>
              )}
              <RenderResponsiveNavHeaderButtons />
            </Stack>
          </Nav>
          {window.location.pathname.split("/")[1] === "hotel" && (
            <Sidebar
              isMobileScreen={isMobileScreen}
              isSidebarOpen={isSidebarOpen}
              handleSidebar={handleSidebar}
            />
          )}
        </>
      ) : (
        <div />
      )}
    </>
  );
};
