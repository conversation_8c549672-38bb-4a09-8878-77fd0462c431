import { useState } from "react";

export const useForm = (data = {}) => {
  const [state, setState] = useState(data);

  const handleChange = (e) => {
    e.persist();

    let value =
      e.target.type === "checkbox" ? e.target.checked : e.target.value;
    if (e.target.name === "rooms" || e.target.name === "minPrice") {
      value = parseInt(value);
    }
    setState((state) => ({ ...state, [e.target.name]: value }));
  };

  return [state, handleChange];
};
