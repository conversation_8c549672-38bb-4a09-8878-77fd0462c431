import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from "react";
import { matchPath, useHistory } from "react-router-dom";
import { BASE_URL } from "./../constants";
import { useFetch } from "./useFetch";
import { useRoutePermissionCheck } from "./useRoutePermissionCheck";

const initialState = {
  user: null,
  hotels: [],
  hotelSelect: null,
  name: null,
  email: null,
  permission: {},
  token: "loading",
  isLoggedIn: false,
  error: null,
  isLoading: false,
  inValidLogin: false,
  isFirstRender: true,
  currentHotel: null,
};

const simpleReducer = (state, payload) => ({ ...state, ...payload });
const AuthContext = createContext();
const { Provider, Consumer } = AuthContext;

const AuthProvider = ({ children }) => {
  const match = matchPath(window.location.pathname, {
    path: ["/hotel/:hotelId"],
    exact: false,
    strict: false,
  });
  const matchResetPasswordUrl = matchPath(window.location.pathname, {
    path: ["/password_reset"],
    exact: false,
    strict: false,
  });

  const [state, setState] = useReducer(simpleReducer, initialState);
  const history = useHistory();
  const searchParams = new URLSearchParams(history.location.search);

  const { apiFetch, cloneMessage, setCloneMessage } = useFetch(state.token);
  const searchValue = searchParams.get("searchHotel") || "";

  const authFetch = React.useCallback(
    (...args) =>
      new Promise((res, rej) => {
        if (state.token !== "loading") {
          res(apiFetch(...args));
        }
      }),
    [state.token, apiFetch]
  );

  const getAllHotels = useCallback(async () => {
    const body = {
      hotelName: searchValue,
    };
    const { get } = await authFetch({
      path: `/managing-org/all/chains/all/hotels/1`,
    });

    const { data, error } = await get(`?${new URLSearchParams(body)}`);

    if (data) {
      setState({ hotels: data?.hotels });
      localStorage.setItem("hotels", JSON.stringify(data));

      return data?.hotels;
    } else {
      setState({ hotels: [] });
    }
  }, [authFetch, searchValue]);

  const updateHotels = () => {
    const token = localStorage.getItem("token");
    const hotels = JSON.parse(localStorage.getItem("hotels"));
    if (token) {
      setState({
        hotels,
      });
    }
  };

  useEffect(() => {
    try {
      const token = localStorage.getItem("token");
      const name = localStorage.getItem("name");
      const email = localStorage.getItem("email");
      const hotels = JSON.parse(localStorage.getItem("hotels"));
      const permission = JSON.parse(localStorage.getItem("permission"));
      const selectedHotel =
        hotels.find((h) => h.id === parseInt(match?.params?.hotelId)) || null;
      if (token) {
        setState({
          token,
          isLoggedIn: true,
          hotels,
          permission,
          hotelSelect: selectedHotel,
          name,
          email,
        });
      }
    } catch (e) {
      setState({
        token: null,
        isLoggedIn: false,
        hotels: [],
        hotelSelect: null,
        permission: {},
        name: null,
        email: null,
      });
    } finally {
      setState({
        isFirstRender: false,
      });
    }
  }, []);

  useEffect(() => {
    if (!!matchResetPasswordUrl) {
      return;
    }
    if (state.isFirstRender) return;
    if (state.token === null) {
      history.push("/");
      return;
    }
    if (
      state.permission[0].name === "listOfOrg" &&
      state.hotelSelect === null &&
      window.location.pathname !== "/hotel"
    ) {
      history.push("/managing_organization");
      return;
    }
    if (
      state.permission[0].name !== "listOfOrg" &&
      state.hotelSelect === null &&
      window.location.pathname !== "/hotel"
    ) {
      history.push("/choose_hotel");
      return;
    }

    if (window.location.pathname === "/hotel" && state.hotelSelect === null) {
      history.push("/hotel");
      return;
    }
  }, [state.token, state.hotelSelect, state.isFirstRender, state.permission]);

  //precium version feature defining globally
  const [version, setVersion] = useState("");
  const [rateShopFeature, setRateShopFeature] = useState(false);
  const [isHotelDetailsPending, setHotelDetailsPending] = useState(false);
  const hotelId = useMemo(() => window.location.href.split("/")[4], [
    window.location.href,
  ]);

  const getHotelDetails = useCallback(
    async (hotelId) => {
      setHotelDetailsPending(true);
      const { get } = await authFetch({
        path: `/hotel/${hotelId}`,
      });
      const { data, error } = await get();
      setState({ currentHotel: { ...data } });
      if (data) {
        setVersion(data.version);
        setRateShopFeature(data.rateShopEnabled);
      } else {
        console.log(error);
      }
      setHotelDetailsPending(false);
    },
    [hotelId, authFetch]
  );

  useEffect(() => {
    if (
      matchPath(window.location.pathname, {
        path: ["/hotel/:hotelId"],
        params: {},
      })
    ) {
      getHotelDetails(hotelId);
    }
  }, [authFetch, state.token, hotelId]);

  useEffect(() => {
    getAllHotels();
  }, []);
  const selectHotel = (hotel, onOrganizationPage = false) => {
    let dashboard = null;
    let bob = null;
    setState({
      hotelSelect: hotel,
    });
    for (let key in state.permission) {
      if (state.permission.hasOwnProperty(key)) {
        if (state.permission[key].name === "dashboard") {
          dashboard = state.permission[key];
        }
        if (state.permission[key].name === "forecastView") {
          bob = state.permission[key];
        }
      }
    }
    if (onOrganizationPage) {
      const url = dashboard
        ? "/hotel/" + hotel.id + "/dashboard"
        : bob
        ? "/hotel/" + hotel.id + "/business_on_books"
        : "/hotel/" + hotel.id + "/setup";
      history.push(url);
    } else if (hotel && state.hotelSelect) {
      const newUrl = window.location.pathname
        .split("/")
        .map((data, index) => {
          if (index === 2) {
            return hotel.id + "";
          } else {
            return data;
          }
        })
        .join("/");
      history.push(newUrl);
    }
  };

  const login = async ({ username, password }) => {
    try {
      setState({ isLoading: true, inValidLogin: false });
      const { token, name, email, hotels, permission } = await fetch(
        `${BASE_URL}/login`,
        {
          method: "POST",
          body: JSON.stringify({ username, password }),
          headers: {
            "Content-type": "application/json; charset=UTF-8",
          },
        }
      ).then((res) => res.json());
      if (token) {
        setState({
          isLoggedIn: true,
          hotels,
          permission,
          token,
          error: null,
          name,
          email,
        });
        localStorage.setItem("token", token);
        localStorage.setItem("name", name);
        localStorage.setItem("email", email);
        localStorage.setItem("hotels", JSON.stringify(hotels));
        localStorage.setItem("permission", JSON.stringify(permission));
      } else {
        setState({
          inValidLogin: true,
          isLoggedIn: false,
          hotels: [],
          token: null,
          hotelSelect: null,
          permission: {},
          name: null,
          email: null,
        });
      }
    } catch (e) {
      setState({ error: "Error logging in" });
    } finally {
      setState({ isLoading: false });
    }
  };
  const logoutApi = useCallback(async () => {
    try {
      const { del } = await authFetch({
        path: `/logout`,
      });

      await del();
    } catch (err) {
      console.log(err);
    }
  }, [authFetch]);

  const logout = () => {
    logoutApi();
    localStorage.removeItem("token");
    localStorage.removeItem("hotels");
    localStorage.removeItem("permission");
    localStorage.removeItem("file");
    localStorage.removeItem("name");
    localStorage.removeItem("email");
    setState({
      user: null,
      token: null,
      isLoggedIn: false,
      hotels: [],
      hotelSelect: null,
      permission: {},
      name: null,
      email: null,
    });
  };
  useRoutePermissionCheck(
    state?.permission ?? [],
    state.hotelSelect?.id,
    rateShopFeature,
    version === "v2",
    state?.currentHotel?.canViewUpcomingEvents ?? true,
    isHotelDetailsPending
  );

  return (
    <Provider
      value={{
        login,
        logout,
        logoutApi,
        selectHotel,
        authFetch,
        cloneMessage,
        setCloneMessage,
        updateHotels,
        ...state,
        version,
        setVersion,
        rateShopFeature,
        setRateShopFeature,
        getHotelDetails,
        getAllHotels,
      }}
    >
      {children}
    </Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
export { AuthProvider, Consumer as AuthConsumer, AuthContext };
