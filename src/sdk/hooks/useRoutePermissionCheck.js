import { useCallback, useEffect } from "react";
import { useHistory } from "react-router-dom";

const routePermissions = {
  //   dashboard: `/dashboard`,
  //   forecastView: `/business_on_books`,
  //   compPriceView: `/rate_comparison`,
  //   PUReport: `/pickup_report`,
  //   analytics: `/analytics`,
  //   pastDataView: `/pastdata`,
  //   priceRecommendationWithoutCalc: `/price_recommendation`,
  //   eventsCalendarView: `/calendar`,
  //   hotelDetailsView: `/setup`,
  showRateshop: `/price-checker`,
  viewRateParity: `/channel-comparison`,
  viewUpcomingEvents: `/event-details`,
};
const rateShopPermissions = [
  "/setup/scheduling",
  "/setup/room-mapping/mapping",
  routePermissions["showRateshop"],
  routePermissions["viewRateParity"],
];
const v2EnabledPermissions = [
  "/forecast/pickup/",
  "/forecast/forecast_sheet_upload",
  "/forecast/forecast_sheet_daily",
  "/forecast/forecast_sheet_monthly",
  "/forecast/forecast_sheet_monthly",
  "/setup/market-segment",
  "/setup/budget-setup",
  "/setup/budget-allocation",
];
export const useRoutePermissionCheck = (
  permission,
  hotelId,
  isRateShopEnabled,
  v2Enabled,
  canViewUpcomingEvents,
  isHotelDetailsPending
) => {
  const history = useHistory();
  const currentPath = window.location.pathname;

  const notAllowedRoutes = [];
  if (Array.isArray(permission)) {
    Object.entries(routePermissions).forEach(([key, val]) => {
      if (!permission.some((per) => key === per?.name)) {
        notAllowedRoutes.push(val);
      } else if (key === "viewUpcomingEvents" && !canViewUpcomingEvents)
        notAllowedRoutes.push(val);
    });
  } else {
    console.warn("permission is null or empty:", permission);
  }

  const routeWithPermissions = [
    ...notAllowedRoutes,
    ...rateShopPermissions,
    ...v2EnabledPermissions,
  ];

  const isRouteAllowed = useCallback(() => {
    if (v2EnabledPermissions.some((path) => currentPath?.includes(path))) {
      if (!v2Enabled) {
        return history.replace(`/hotel/${hotelId}/dashboard`);
      }
      return;
    } else if (
      rateShopPermissions.some((path) => currentPath?.includes(path))
    ) {
      if (!isRateShopEnabled) {
        return history.replace(`/hotel/${hotelId}/dashboard`);
      }
    } else if (notAllowedRoutes.some((path) => currentPath?.includes(path))) {
      return history.replace(`/hotel/${hotelId}/dashboard`);
    }
  }, [
    notAllowedRoutes,
    currentPath,
    history,
    hotelId,
    isRateShopEnabled,
    v2Enabled,
  ]);

  useEffect(() => {
    if (!hotelId || isHotelDetailsPending) return;

    if (routeWithPermissions?.some((path) => currentPath?.includes(path)))
      isRouteAllowed();
  }, [
    currentPath,
    hotelId,
    isHotelDetailsPending,
    isRouteAllowed,
    routeWithPermissions,
  ]);
};
