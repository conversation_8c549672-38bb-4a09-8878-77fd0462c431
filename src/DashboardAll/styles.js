import { Box, Tooltip, Typography, styled } from "@mui/material";

export const Page = styled(Box)`
  padding: 70px 20px 40px 80px;
  height: 100%;
`;
export const Container = styled(Box)`
  width: 100%;
  height: 100%;
  background: #ffffff 0% 0% no-repeat padding-box;
  margin: 12px 20px 20px 0;
  padding: 16px 40px;
  box-shadow: 0px 4px 4px 0px #00000040;
  border-radius: 6px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  column-gap: 10px;
  row-gap: 0px;
  align-items: stretch;
`;

export const Card = styled(Box)`
  width: 100%;
  height: 94%;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

export const Label = styled(Typography)`
  font-size: 20px;
  font-color: #303030;
  font-family: Roboto;
`;
export const LabelNew = styled(Label)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 18px;

  color: #303030;
`;
export const SimpleGraphCard = styled(Box)`
  width: 100%;
  padding: 8px;
  height: 94%;
  display: flex;
  flex-direction: column;
  justify-content: start;
  border-radius: 8px;
`;
export const Header = styled(Box)`
  display: flex;

  justify-content: space-between;
  align-items: baseline;
  width: auto;
  padding-bottom: 8px;
`;

export const CardName = styled(Typography)`
  text-align: left;
  font: normal normal normal 18px/20px Roboto;
  letter-spacing: 0px;
  color: #000000;
`;

export const CardDate = styled(Typography)`
  text-align: left;
  font: normal normal normal 14px/17px Roboto;
  letter-spacing: 0px;
  color: #000000;
`;
export const Body = styled(Box)``;

export const Info = styled(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex: 1;
  width: 100%;
`;

export const Details = styled(Box)`
  justify-content: space-between;
  align-items: center;
  padding-left: 4px;
`;

export const Bookings = styled(Box)`
  display: flex;

  justify-content: flex-start;
  align-items: center;
  padding: 8px 0 8px 0;
`;

export const ARR = styled(Box)`
  display: flex;

  justify-content: flex-start;
  align-items: center;
  padding: 8px 0 8px 0;
  flex: 1;
  text-align: left;
`;
export const Revenue = styled(Box)`
  display: flex;

  justify-content: flex-start;
  align-items: center;

  padding: 8px 0 8px 0;
  flex: 1;
`;
export const RevPAR = styled(Box)`
  display: flex;

  justify-content: flex-start;
  align-items: center;

  padding: 8px 0 8px 0;
  flex: 1;
`;

export const Title = styled(Typography)`
  text-align: center;
  font: normal normal bold 14px/17px Roboto;
  letter-spacing: 0px;
  color: #110641;
  width: 96px;
  padding: 0 4px 0 4px;
`;
export const TitleBox = styled(Typography)`
  text-align: center;
  font: normal normal bold 14px/17px Roboto;
  letter-spacing: 0px;
  color: #110641;
  width: 96px;
  padding: 0 0px 0 4px;
`;

export const Value = styled(Typography)`
  text-align: left;
  font: normal normal normal 14px/17px Roboto;
  letter-spacing: 0px;
  color: #1d1d1d;
  padding: 0 4px 0 4px;
`;

export const Year = styled(Typography)`
  text-align: left;
  font: normal bold normal 18px/19px Roboto;
  letter-spacing: 0px;
  color: #000000;
  display: flex;
  justify-content: center;
`;
