import React from "react";
import { styled } from "@mui/system";
import { formatNumber, useAuth } from "../sdk";
import {
  ARR,
  Body,
  Bookings,
  Card,
  CardDate,
  CardName,
  Details,
  Header,
  Info,
  Revenue,
  RevPAR,
  Title,
  TitleBox,
  Value,
} from "./styles";

import { Typography, Box } from "@mui/material";
import CircularProgress from "@mui/material/CircularProgress";
const MONTHSARRAY = [
  "Jan",
  "Feb",
  "Mar",
  "Apr",
  "May",
  "Jun",
  "Jul",
  "Aug",
  "Sept",
  "Oct",
  "Nov",
  "Dec",
];

const Head = styled(Box)``;
const Occupancy = styled(Typography)`
  text-align: center;
  font: normal normal normal 32px/36px Roboto;
  letter-spacing: 0px;
  color: #1d1d1d;
  display: flex;
`;
const CardNameNew = styled(CardName)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 20px;

  text-transform: capitalize;

  color: #303030;
`;
const TitleNew = styled(Title)`
  font-family: Roboto;
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 14px;
  color: #000000;
  text-align: left;
`;
const ValueNew = styled(Value)`
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 14px;

  text-transform: capitalize;

  color: #000000;
`;

const CardDateNew = styled(CardDate)`
  font-family: Roboto;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 18px;

  text-transform: capitalize;

  color: #777777;
`;
const BorderLIne = styled(Box)`
  border: 1px solid rgba(0, 0, 0, 0.25);
  transform: rotate(180deg);
  height: 118px;
  width: 2px;
`;
const HeaderNew = styled(Header)`
  margin-bottom: 4px;
  margin-top: 5px;
`;

export default function NightPerformanceCard({ data }) {
  const [lastNightDay, lastNightMonth, lastNightYear] = new Date(
    new Date(data?.date).setDate(new Date(data?.date).getDate() - 1)
  )
    .toLocaleDateString("en-GB")
    .split("/");

  const lastNightDate =
    lastNightDay +
    " " +
    MONTHSARRAY[parseInt(lastNightMonth) - 1] +
    " " +
    lastNightYear;
  const CircularProgressBox = () => {
    return (
      <Box style={{ position: "relative" }}>
        <CircularProgress
          variant="determinate"
          sx={{
            color: "#E3E3E3",
          }}
          size={180}
          thickness={4}
          value={100}
        />
        <CircularProgress
          variant="determinate"
          sx={{
            color: "#306FBC",
            position: "absolute",
            left: 0,
          }}
          size={180}
          thickness={4}
          value={
            parseFloat(data?.occupancyPercentage).toFixed(2) >= 100
              ? 100
              : parseFloat(data?.occupancyPercentage).toFixed(2)
          }
        />
        <Box
          style={{
            top: 0,
            left: 0,
            marginLeft: 0,
            bottom: 0,
            right: 0,
            position: "absolute",
            display: "flex",
            alignItems: "center",
            justifyContent: "start",
          }}
        >
          <Typography
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "end",
            }}
            variant="caption"
            component="Box"
            color="text.secondary"
          >
            <Box
              style={{
                display: "flex",
                justifyContent: "center",
                fontFamily: "Roboto",
                fontStyle: "normal",
                fontWeight: "500",
                fontSize: "18px",
                lineHeight: "20px",
                color: "#000000 ",
                textTransform: "capitalize",
              }}
            >
              {`${parseFloat(data?.occupancyPercentage).toFixed(2)}%`}
            </Box>
            <Head
              sx={{
                display: "flex",
                flexDirection: "column",
                alignItems: "end",
              }}
            >
              <TitleBox
                style={{
                  width: "114px",
                  fontFamily: "Roboto",
                  fontStyle: "normal",
                  fontWeight: "normal",
                  fontSize: "12px",
                  lineHeight: "14px",
                  paddingRight: "0px !important",
                  textTransform: "capitalize",

                  color: "#000000 ",
                  display: "flex",
                  justifyContent: "end",
                }}
              >
                Occupancy
              </TitleBox>
            </Head>
          </Typography>
        </Box>
      </Box>
    );
  };

  return (
    <Card
      style={{
        boxShadow: "1px 4px 10px rgba(48, 111, 188, 0.2)",
        padding: "16px 20px",
      }}
    >
      <HeaderNew>
        <CardNameNew
          sx={{
            margin: "auto",
          }}
        >
          Last Night Performance
        </CardNameNew>
        <CardDateNew>{lastNightDate}</CardDateNew>
      </HeaderNew>
      <Body display={"flex"} alignItems={"center"} height={"100%"}>
        <Info
          style={{ display: "flex", justifyContent: "space-around", gap: 2 }}
        >
          <Details>
            <Box>
              <CircularProgressBox />
            </Box>
          </Details>
          <Details
            sx={{
              display: "flex",
              justifyContent: "center !important",
            }}
          >
            <BorderLIne />
          </Details>
          <Details>
            <Bookings
              sx={{
                gap: 2,
              }}
            >
              <TitleNew>Rooms Sold</TitleNew>
              <ValueNew>
                {formatNumber(parseInt(data?.lastNight?.booking))}
              </ValueNew>
            </Bookings>
            <ARR
              sx={{
                gap: 2,
              }}
            >
              <TitleNew>ADR</TitleNew>
              <ValueNew>
                {formatNumber(parseFloat(data?.lastNight?.arr).toFixed(2))}
              </ValueNew>
            </ARR>
            <Revenue
              sx={{
                gap: 2,
              }}
            >
              <TitleNew>Revenue</TitleNew>
              <ValueNew>{formatNumber(data?.lastNight?.revenue)}</ValueNew>
            </Revenue>
            <RevPAR
              sx={{
                gap: 2,
              }}
            >
              <TitleNew>RevPAR</TitleNew>
              <ValueNew>
                {formatNumber(parseFloat(data?.lastNight?.revPAR).toFixed(2))}
              </ValueNew>
            </RevPAR>
          </Details>
        </Info>
      </Body>
    </Card>
  );
}
