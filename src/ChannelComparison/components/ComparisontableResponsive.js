import {
  Card,
  CircularProgress,
  Container,
  Grid,
  Stack,
  styled,
  Typography,
} from "@mui/material";
import React, { useCallback } from "react";
export const ComparisonTableResponsive = ({
  channels,
  tableData,
  isLoading,
  currencies,
  currentHotel,
}) => {
  const brandWebsite = channels?.find((channel) => channel?.isBrandWebsite);
  const brandWebsiteData = useCallback(
    (data) => {
      if (!brandWebsite) return null;

      return data?.hotelResult?.sourceSortedArray?.find(
        (hotel) => hotel?.sourceId === brandWebsite?.sourceId
      );
    },
    [brandWebsite]
  );
  if (isLoading) {
    return (
      <Container
        sx={{
          textAlign: "center",
        }}
      >
        <CircularProgress />
      </Container>
    );
  }
  return (
    <StyledStackWrapper>
      {tableData?.filter((i) => i?.hotelResult)?.length ? (
        tableData?.map(
          (data, index) =>
            Boolean(data?.hotelResult?.sourceSortedArray?.length) && (
              <Card key={`rateparityCard-${index}`} className="comparisonCard">
                <Typography fontWeight={500} textAlign="center" variant="body1">
                  Date: {data?.rateDate}
                </Typography>
                <Grid container rowGap={0.5}>
                  {brandWebsiteData(data) && (
                    <Grid
                      item
                      xs={6}
                      spacing={2}
                      key={`brandWebsite-${index}`}
                      className="hotelTableGrid"
                    >
                      <Typography
                        sx={{
                          width: "fit-content",
                        }}
                      >
                        Brand Website -{" "}
                      </Typography>
                      <Typography
                        fontWeight={500}
                        sx={{
                          width: "fit-content",
                        }}
                      >
                        {brandWebsiteData(data)?.isSoldOut
                          ? "Sold out"
                          : `$${brandWebsiteData(data)?.price}`}
                      </Typography>
                    </Grid>
                  )}
                  {data?.hotelResult?.sourceSortedArray?.map(
                    (hotel) =>
                      channels?.some((i) => i?.sourceId === hotel?.sourceId) &&
                      brandWebsite?.sourceId !== hotel?.sourceId && (
                        <Grid
                          item
                          xs={6}
                          spacing={2}
                          key={`hotels-${hotel?.sourceId}`}
                          className="hotelTableGrid"
                        >
                          <Typography
                            sx={{
                              width: "fit-content",
                            }}
                          >
                            {channels?.find(
                              ({ sourceId }) => sourceId === hotel?.sourceId
                            )?.isBrandWebsite
                              ? "Brand Website"
                              : channels?.find(
                                  ({ sourceId }) => sourceId === hotel?.sourceId
                                )?.name}
                            -{" "}
                          </Typography>
                          <Typography
                            fontWeight={500}
                            sx={{
                              width: "fit-content",
                            }}
                          >
                            {hotel && hotel?.internalStatus === "available"
                              ? (currencies?.[currentHotel.RSCurrency]
                                  ?.symbol ?? currentHotel.RSCurrency) +
                                hotel?.price
                              : null}
                            {hotel?.isSoldOut ? "Sold out" : ""}
                          </Typography>
                        </Grid>
                      )
                  )}
                </Grid>
              </Card>
            )
        )
      ) : (
        <Typography textAlign="center">No rates available</Typography>
      )}
    </StyledStackWrapper>
  );
};

const StyledStackWrapper = styled(Stack)(() => ({
  gap: 16,
  padding: "16px 16px 65px",
  overflow: "auto",
  ".comparisonCard": {
    height: "111px",
    borderRadius: "8px",
    padding: "5px",
    border: "1px solid #717171",
  },
  ".hotelTableGrid": {
    display: "flex",
    flexDirection: "row",
    gap: "5px",
    ".hotelTypography": {
      color: "red",
      width: "fit-content",
    },
  },
}));
