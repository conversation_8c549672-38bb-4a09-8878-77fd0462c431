import {
  Box,
  Skeleton,
  Stack,
  TableBody,
  Tooltip,
  Typography,
  styled,
} from "@mui/material";
import Table from "@mui/material/Table";
import ReportProblemIcon from "@mui/icons-material/ReportProblem";
import TableCell, { tableCellClasses } from "@mui/material/TableCell";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import React from "react";
import { useAuth } from "../../sdk";
import { Page } from "../Styles";
import { format } from "date-fns/esm";
import { currencies } from "../../sdk/codeConstant";
import { ComparisonTableResponsive } from "./ComparisontableResponsive";

let rows = new Array(25).fill({
  date: "Wed, 01/02/2023",
  brand: 45,
  booking: 345,
  expedia: 781,
});

const ClassWrapper = styled(Box)(() => ({
  ".scrollContainer": {
    overflow: "auto",
    maxHeight: "70vh",
    "&::-webkit-scrollbar": {
      width: "6px",
    },
    "&::-webkit-scrollbar-thumb": {
      backgroundColor: "#163A90",
      borderRadius: "6px",
    },
  },
  ".MuiTableRow-head": {
    backgroundColor: "#306fbc",
  },
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: "#306fbc",
    color: "white",
    // border: 0,
  },
}));

const ComparisonTable = ({
  channels,
  tableData,
  isLoading,
  showSkeleton,
  isMobileScreen,
}) => {
  const { currentHotel } = useAuth();
  // add index property to each row object
  const newDate = new Date();
  const todaysDate = format(newDate, "yyyy-MM-dd");
  const brandWebsite = channels?.find((channel) => channel?.isBrandWebsite);

  const nonBrandWebsites = channels?.filter(
    (channel) => !channel?.isBrandWebsite
  );
  const sortedChannels = brandWebsite
    ? [brandWebsite, ...nonBrandWebsites]
    : nonBrandWebsites;
  channels = sortedChannels;

  const isUniqueHighestPriceBySource = (row, sourceId, isBrandWebsite) => {
    const targetHotel = row?.hotelResult?.sourceSortedArray.find(
      (item) => item?.sourceId === sourceId
    );

    if (
      !targetHotel ||
      targetHotel.price === undefined ||
      targetHotel?.internalStatus !== "available"
    )
      return false; // No valid price

    const targetPrice = targetHotel.price;

    let otherPrices = [];

    if (isBrandWebsite) {
      otherPrices = row?.hotelResult?.sourceSortedArray
        ?.filter(
          (item) =>
            item?.internalStatus === "available" && item?.sourceId !== sourceId
        )
        ?.map((item) => item?.price);
    } else {
      otherPrices = row?.hotelResult?.sourceSortedArray
        ?.filter(
          (item) =>
            item?.internalStatus === "available" &&
            item?.sourceId !== sourceId &&
            item?.sourceId !== 8 &&
            item?.sourceId !== 3674
        )
        ?.map((item) => item?.price);
    }

    return (
      otherPrices.length > 0 &&
      otherPrices.every((price) => targetPrice > price) &&
      !otherPrices.includes(targetPrice)
    );
  };

  const isUniqueLowestPriceBySource = (row, sourceId, isBrandWebsite) => {
    const targetHotel = row?.hotelResult?.sourceSortedArray.find(
      (item) => item?.sourceId === sourceId
    );

    if (
      !targetHotel ||
      targetHotel.price === undefined ||
      targetHotel?.internalStatus !== "available"
    )
      return false; // No valid price

    const targetPrice = targetHotel.price;

    let otherPrices = [];
    if (isBrandWebsite) {
      otherPrices = row?.hotelResult?.sourceSortedArray
        ?.filter(
          (item) =>
            item?.internalStatus === "available" && item?.sourceId !== sourceId
        )
        ?.map((item) => item?.price);
    } else {
      otherPrices = row?.hotelResult?.sourceSortedArray
        ?.filter(
          (item) =>
            item?.internalStatus === "available" &&
            item?.sourceId !== sourceId &&
            item?.sourceId !== 8 &&
            item?.sourceId !== 3674
        )
        ?.map((item) => item?.price);
    }

    return (
      otherPrices.length > 0 &&
      otherPrices.every((price) => targetPrice < price) &&
      !otherPrices.includes(targetPrice)
    );
  };

  const handlePriceColor = (row, sourceId) => {
    const isBrandWebsite = sourceId === 8 || sourceId === 3674;

    const isLowest = isUniqueLowestPriceBySource(row, sourceId, isBrandWebsite);
    const isHighest = isUniqueHighestPriceBySource(
      row,
      sourceId,
      isBrandWebsite
    );

    if (isHighest || (isBrandWebsite && !isLowest)) return "red";
    if (isLowest) return isBrandWebsite ? "green" : "red";

    return "inherit";
  };

  if (isMobileScreen)
    return (
      <ComparisonTableResponsive
        isLoading={isLoading}
        tableData={tableData}
        currentHotel={currentHotel}
        channels={channels}
        currencies={currencies}
      />
    );

  return (
    <Page>
      <ClassWrapper>
        {isLoading && showSkeleton ? (
          [...Array(7)].map((_, index) => (
            <Skeleton
              key={index}
              variant="rounded"
              height={40}
              width="90%"
              sx={{ mt: 5 }}
            />
          ))
        ) : (
          <Box
            sx={{
              width: "90%",
              borderRadius: "5px",
            }}
          >
            <div className="scrollContainer">
              <Table
                stickyHeader
                sx={{
                  border: "0.5px solid #ABABAB",
                }}
                size="small"
              >
                <TableHead>
                  <TableRow
                    sx={{
                      height: "62px",
                    }}
                  >
                    <StyledTableCell
                      align="center"
                      sx={{
                        borderRight: "1px solid #ABABAB",
                      }}
                    >
                      Date
                    </StyledTableCell>
                    {channels?.map((channel) => {
                      if (channel?.isBrandWebsite) {
                        return (
                          <StyledTableCell align="center" key={channel?.id}>
                            Brand Website
                          </StyledTableCell>
                        );
                      } else {
                        return (
                          <StyledTableCell align="center" key={channel?.id}>
                            {channel?.name}
                          </StyledTableCell>
                        );
                      }
                    })}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tableData.map((row) => (
                    <StyledTableRow
                      key={row.rateDate}
                      sx={{
                        height: "48px",
                        background: row.index === 0 ? "#DFDFDF" : "",
                        "& td": {
                          color:
                            row.rateDate === todaysDate ? "#306FBC" : "#000000",
                        },
                      }}
                    >
                      <TableCell
                        align="center"
                        component="th"
                        scope="row"
                        sx={{
                          width: "250px",
                          fontSize: "14px",
                          fontWeight: 600,
                          color:
                            row.rateDate === todaysDate ? "#306FBC" : "#000000",
                          borderRight: "1px solid #ABABAB",
                        }}
                      >
                        <Stack
                          direction="row"
                          justifyContent="center"
                          alignItems="center"
                          gap="16px"
                        >
                          {row.rateDate}
                          {row?.isSearching && (
                            <StyledSearching
                              component="img"
                              src="/assets/loading.svg"
                              alt="refreshing"
                              title="Processing"
                            />
                          )}
                        </Stack>
                      </TableCell>
                      {channels
                        ?.map(
                          ({ sourceId }) =>
                            row?.hotelResult?.sourceSortedArray?.find(
                              (channel) => channel?.sourceId === sourceId
                            ) ?? { price: null }
                        )
                        .map((source) => {
                          const brandWebsitePrice = row?.hotelResult?.sourceSortedArray.find(
                            (item) =>
                              item?.sourceId === 8 || item?.sourceId === 3674 // hard coded source id for brand website
                          )?.price;

                          const higherthanOta = row?.hotelResult?.sourceSortedArray?.some(
                            (item) =>
                              item?.internalStatus === "available" &&
                              brandWebsitePrice > item?.price
                          );

                          return (
                            <TableCell align="center" key={source?.sourceId}>
                              <Typography
                                fontSize="unset"
                                color={handlePriceColor(row, source?.sourceId)}
                              >
                                {source &&
                                source?.internalStatus === "available"
                                  ? (currencies?.[currentHotel.RSCurrency]
                                      ?.symbol ?? currentHotel.RSCurrency) +
                                    source?.price
                                  : null}
                                {source && source?.internalStatus === "sold_out"
                                  ? "Sold Out"
                                  : null}
                                {source &&
                                source?.internalStatus === "error" ? (
                                  <Tooltip
                                    color="error"
                                    sx={{
                                      fontSize: "20px",
                                    }}
                                    title={source.errMsg}
                                  >
                                    <ReportProblemIcon />
                                  </Tooltip>
                                ) : null}
                                {!source.price && source?.price !== 0
                                  ? "--"
                                  : null}
                              </Typography>
                            </TableCell>
                          );
                        })}
                    </StyledTableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Box>
        )}
      </ClassWrapper>
    </Page>
  );
};

export default ComparisonTable;

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: "rgba(116, 116, 116, 0.2)",
  },
  "& td": {
    border: 0,
  },
}));

const StyledSearching = styled(Box)(() => ({
  height: "30px",
  aspectRatio: "1/1",
  animation: "spin 1s infinite linear",

  "@keyframes spin": {
    from: {
      transform: "rotate(0deg)",
    },
    to: {
      transform: "rotate(360deg)",
    },
  },
}));
