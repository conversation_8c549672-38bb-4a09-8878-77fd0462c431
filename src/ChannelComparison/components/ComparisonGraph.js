import { Box } from "@mui/system";
import da from "date-fns/esm/locale/da/index";
import React, { useMemo } from "react";
import { Bar } from "react-chartjs-2";
import { Page } from "../Styles";

function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

const websiteColorPalette = [
  "#886F6F",
  "#EE33BB",
  "#D67D3E",
  "#B8405E",
  "#BF9270",
  "#694E4E",
  "#5B2C6F",
  "#7FFF00",
  "#455A64",
];

// const data = {
//     labels,
//     datasets: [
//         {
//             label: "Brand.com",
//             data: labels.map(() => getRandomInt(250, 1000)),
//             backgroundColor: "#C85633",
//         },
//         {
//             label: "Booking.com",
//             data: labels.map(() => getRandomInt(250, 1000)),
//             backgroundColor: "#6430C6",
//         },
//         {
//             label: "Expedia",
//             data: labels.map(() => getRandomInt(250, 1000)),
//             backgroundColor: "#EC83E0",
//         },
//     ],
// };

export const ComparisonGraph = ({ channels, tableData, selectedMonthYear }) => {
  const max = useMemo(() => {
    let max = 0;
    channels.forEach((channel) => {
      tableData.forEach((data) => {
        const price = data?.hotelResult?.sourceSortedArray?.find(
          ({ sourceId }) => sourceId === channel?.sourceId
        )?.price;
        if (price > max) {
          max = price;
        }
      });
    });

    if (max > 1000) {
      const a = Math.floor(max / 500);
      max = 500 * (a + 1);
    }
    return max;
  }, [tableData, channels]);
  const options = useMemo(
    () => ({
      responsive: true,
      legend: {
        position: "bottom",
        labels: {
          fontFamily: "Roboto",
          padding: 20,
          fontSize: 14,
          fontWeight: 500,
          fontColor: "black",
          boxWidth: 15,
        },
      },
      elements: {
        line: {
          fill: false,
        },
      },
      maintainAspectRatio: false,
      scales: {
        xAxes: [
          {
            offset: true,
            display: true,
            gridLines: {
              color: "black",
              display: true,
              drawTicks: true,
              drawOnChartArea: false,
            },
            ticks: {
              fontFamily: "Roboto",
              fontColor: "#000000",
              fontSize: 13,
              fontWeight: 400,
            },
          },
        ],
        yAxes: [
          {
            type: "linear",
            display: true,
            gridLines: {
              color: "black",
              display: true,
              drawTicks: true,
              drawOnChartArea: false,
            },
            position: "left",
            ticks: {
              beginAtZero: false,
              fontFamily: "Roboto",
              fontColor: "#000000",
              fontSize: 13,
              fontWeight: 400,
              min: 0,
              max: max > 1000 ? max : 1000,
              stepSize: max > 1000 ? max / 10 : 100,
            },
          },
        ],
      },
    }),
    [max]
  );

  const labels = useMemo(() => {
    return Array.from(
      { length: 30 },
      (_, i) =>
        `${("0" + (i + 1)).slice(-2)}/${(
          "0" +
          (selectedMonthYear.getMonth() + 1)
        ).slice(-2)}`
    ).map(String);
  }, [selectedMonthYear]);

  const data = useMemo(
    () => ({
      labels,
      datasets: channels?.map((channel, index) => ({
        label: channel?.name,
        backgroundColor: websiteColorPalette[index],
        data: tableData?.map(
          (data) =>
            data?.hotelResult?.sourceSortedArray?.find(
              ({ sourceId }) => sourceId === channel?.sourceId
            )?.price
        ),
      })),
    }),
    [channels, tableData, labels]
  );
  return (
    <Page>
      <Box sx={{ height: "330px", width: "90%", mt: 1 }}>
        <Bar options={options} data={data} height={339} width={null} />
      </Box>
    </Page>
  );
};
