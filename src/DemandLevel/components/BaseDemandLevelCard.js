// import React from 'react'
// import Table from '@material-ui/core/Table';
// import TableBody from '@material-ui/core/TableBody';
// import Paper from '@material-ui/core/Paper';
// import GridListTile from '@material-ui/core/GridListTile';
// import { SaveNEditBtn } from '../../sdk';
// import {
//     CardContainer,
//     TContainer,
//     TRow,
//     TCell as TC,
//     THead,
//     TextFieldSmall,
//     UploadBtn,
//     Head
// } from '../Styles';
// import styled from 'styled-components';
// import { DatePicker as DP } from "@material-ui/pickers";
// import Select from '@material-ui/core/Select';
// import MenuItem from '@material-ui/core/MenuItem';
// import Modal from '@material-ui/core/Modal'
// import {BaseDemandModel} from './BaseDemandModel'
// import Snackbar from '@material-ui/core/Snackbar';

// const TCell = styled(TC)`
//     &.MuiTableCell-root{
//         &:nth-child(2){
//             padding: ${props => props.isInEditMode ? '0 20px 0 0' : '5px 20px 5px 0'};
//         }
//     }
// `;

// const DatePicker = styled(DP)`
//     &.MuiFormControl-root{
//         input{
//             padding: 0;
//             width: 90px;
//         }
//         .MuiInput-underline.MuiInputBase-formControl{
//             height: 29px;
//         }
//     }
// `;
// const Days = [
//     'Sun',
//     'Mon',
//     'Tue',
//     'Wed',
//     'Thu',
//     'Fri',
//     'Sat'
// ];

// const TextFieldBig = styled(TextFieldSmall)`
//     width: 260px;
// `;

// export const  BaseDemandLevelCard = ({ baseDemandLevels, demandLevels, isInEditMode, setIsInEditMode, onChange ,open,handleCloseStatus,networkMsg,state,addRow,patchRowValue,setFromSetModel}) =>{
//     function withValidId(demandLevel) {
//         return !!demandLevel.id
//     }
//     const [openModel, setOpenModel] = React.useState(false);
//     return (
//         <GridListTile cols={3}>
//             <CardContainer>

//                 <Head>
//                 <div className="title">
//                     Base Demand Level
//                 </div>
//                 <UploadBtn onClick={() => setOpenModel(true)}>
//                         Set
//                         </UploadBtn>
//                     <Modal
//                         open={openModel}
//                         onClose={() => setOpenModel(false)}
//                     >
//                         <BaseDemandModel
//                         setOpenModel={setOpenModel}
//                         state={state}
//                         addRow={addRow}
//                         patchRowValue={patchRowValue}
//                         setFromSetModel={setFromSetModel}
//                            />
//                     </Modal>
//                     </Head>

//                     <SaveNEditBtn onClick={() => setIsInEditMode(!isInEditMode)} isInEditMode={isInEditMode}>
//                     {isInEditMode ? 'Save' : 'Edit'}
//                 </SaveNEditBtn>
//                 <TContainer component={Paper}>
//                     <Table aria-label="customized table">
//                         <THead>
//                             <TRow>
//                                 <TCell align="center">Date</TCell>
//                                 <TCell align="center">Demand Level</TCell>
//                                 <TCell align="center">Reason</TCell>
//                             </TRow>
//                         </THead>
//                         <TableBody>
//                             {baseDemandLevels.map((row, idx) => (
//                                 (row.demandLevelSymbol || isInEditMode) &&
//                                 <TRow key={row.date}>
//                                     <TCell align="center" isInEditMode={isInEditMode}>
//                                         {
//                                         //   (row.demandLevelSymbol || isInEditMode) ?
//                                         row.date
//                                         }  ({Days[(new Date(row.date)).getDay()]})
//                                     </TCell>
//                                     <TCell align="center" isInEditMode={isInEditMode}>
//                                         {
//                                             isInEditMode ? (
//                                                 <Select
//                                                     value={row.demandLevelSymbol}
//                                                     onChange={(e) => onChange(idx, 'demandLevelSymbol', e.target.value)}
//                                                 >
//                                                     {
//                                                         demandLevels.filter(withValidId).map(dL => (
//                                                             <MenuItem value={dL.symbol} key={dL.id}>{dL.symbol}</MenuItem>
//                                                         ))
//                                                     }

//                                                 </Select>
//                                             ) : (row.demandLevelSymbol)
//                                         }
//                                     </TCell>
//                                     <TCell align="center" isInEditMode={isInEditMode}>
//                                         {
//                                             isInEditMode ? (
//                                                 <TextFieldBig
//                                                     multiline
//                                                     value={row.description}
//                                                     onChange={(e) => onChange(idx, 'description', e.target.value)} />
//                                             ) : (row.description || '-')
//                                         }
//                                     </TCell>
//                                 </TRow>
//                             ))}
//                         </TableBody>
//                     </Table>
//                 </TContainer>
//             </CardContainer>
//             <Snackbar
//                 anchorOrigin={{
//                     vertical: 'bottom',
//                     horizontal: 'left',
//                 }}
//                 open={open}
//                 autoHideDuration={4000}
//                 onClose={handleCloseStatus}
//                 message={networkMsg}
//             />
//         </GridListTile>
//     )
// }
