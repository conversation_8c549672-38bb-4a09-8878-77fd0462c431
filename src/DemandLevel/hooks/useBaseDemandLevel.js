import { useEffect, useState } from "react";
import {
  BASE_URL,
  getISODate,
  onUniqueProp,
  useAuth,
  usePrevious,
} from "../../sdk";
const Days = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];
export function useBaseDemandLevel(hotelId, copyFromHotelId, BaseDemandLevel) {
  const [baseDemandLevels, setBaseDemandLevels] = useState([]);
  const [isInEditMode, setIsInEditMode] = useState(false);
  const saveButtonClicked = usePrevious(isInEditMode);
  const [open, setOpen] = useState(false);

  const [fromSetModel, setFromSetModel] = useState(false);
  const emptyRow = {
    baseDemandLevel: null,
    reason: null,
    daysSelecton: [],
    startDate: new Date(),
    endDate: new Date(new Date().setDate(new Date().getDate() + 1)),
  };
  const [state, setState] = useState({ rows: [emptyRow] });

  const addRow = () => {
    const everyRowIsValid = state.rows.every(isVaildEntry);
    if (everyRowIsValid) {
      setState({
        rows: [
          ...state.rows,
          {
            ...emptyRow,
            startDate: new Date(
              new Date(state.rows[state.rows.length - 1].endDate).setDate(
                new Date(state.rows[state.rows.length - 1].endDate).getDate() +
                  1
              )
            ),
            endDate: new Date(
              new Date(state.rows[state.rows.length - 1].endDate).setDate(
                new Date(state.rows[state.rows.length - 1].endDate).getDate() +
                  2
              )
            ),
          },
        ],
      });
    }
  };
  function patchRowValue(key, value, index) {
    setState((prevState) => {
      return {
        rows: prevState.rows.map((row, idx) => {
          if (idx === index) {
            return {
              ...row,
              [key]: value,
            };
          }
          return row;
        }),
      };
    });
  }
  function isVaildEntry(pricingData) {
    return (
      pricingData.startDate &&
      pricingData.endDate &&
      pricingData.baseDemandLevel
    );
  }

  useEffect(() => {
    if (fromSetModel) {
      var baseDemandArray = [];
      state.rows.map((data) => {
        for (
          var d = new Date(data.startDate);
          d <= new Date(data.endDate);
          d.setDate(d.getDate() + 1)
        ) {
          if (data.daysSelecton.some((day) => day === Days[d.getDay()])) {
            baseDemandArray.push({
              demandLevelSymbol: data.baseDemandLevel,
              description: data.reason,
              date: new Date(d),
            });
          }
        }
      });

      setBaseDemandLevels([...baseDemandLevels, ...baseDemandArray]);
      setFromSetModel(false);
      setState({ rows: [emptyRow] });
    } else {
      updateBaseDemandLevels();
    }
  }, [fromSetModel]);

  const handleCloseStatus = () => {
    setOpen(false);
  };

  const [networkMsg, setnetworkMsg] = useState(null);
  const { token } = useAuth();
  useEffect(() => {
    if (!token) return;
    refreshBaseDemandLevels();
  }, [token, hotelId]);
  useEffect(() => {
    if (saveButtonClicked) {
      updateBaseDemandLevels();
    }
  }, [isInEditMode]);
  useEffect(() => {
    if (copyFromHotelId === null && !BaseDemandLevel) return;
    refreshBaseDemandLevels(copyFromHotelId);
  }, [BaseDemandLevel]);

  function isValid(bDL) {
    return bDL.demandLevelSymbol !== null;
  }
  function generateEmptyDemandLevelForNext6Months() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return Array(183)
      .fill(null)
      .map((_, index) => {
        const date = new Date();
        date.setHours(0, 0, 0, 0);
        date.setDate(today.getDate() + index);
        return {
          demandLevelSymbol: null,
          date: date,
          description: null,
        };
      });
  }
  function generateBaseDemandLevelRows(bDLs) {
    return [...bDLs, ...generateEmptyDemandLevelForNext6Months()]
      .map((bDL) => ({
        ...bDL,
        date: new Date(bDL.date),
      }))
      .filter(onUniqueProp("date"))
      .sort((a, b) => {
        const [dateA, monthA, yearA] = a.date
          .toLocaleDateString("en-GB")
          .split("/");
        const [dateB, monthB, yearB] = b.date
          .toLocaleDateString("en-GB")
          .split("/");
        return (
          Date.parse(`${monthA - 1}/${dateA}/${yearA}`) -
          Date.parse(`${monthB - 1}/${dateB}/${yearB}`)
        );
      });
  }
  async function refreshBaseDemandLevels(anotherHotelId = false) {
    const hotelIdToUse = anotherHotelId ? anotherHotelId : hotelId;
    const bDLs = await fetch(
      `${BASE_URL}/hotel/${hotelIdToUse}/base-demand-level`,
      {
        headers: {
          token,
        },
        method: "GET",
      }
    )
      .then((res) => res.json())
      .catch((err) => {
        setnetworkMsg("can't fetch base demand level data");
        setOpen(true);
        console.log(err);
      });
    if (bDLs) setBaseDemandLevels(generateBaseDemandLevelRows(bDLs));
    if (anotherHotelId) {
      setIsInEditMode(true);
    }
  }
  async function updateBaseDemandLevels() {
    await fetch(`${BASE_URL}/hotel/${hotelId}/base-demand-level/all`, {
      headers: {
        token,
      },
      method: "POST",
      body: JSON.stringify(
        baseDemandLevels.filter(isValid).map((bDL) => {
          const [date, month, year] = bDL.date
            .toLocaleDateString("en-GB")
            .split("/");
          return {
            ...bDL,
            date: getISODate(new Date(year, month - 1, date)),
          };
        })
      ),
    }).catch((err) => {
      setnetworkMsg("can't update Base demand level");
      setOpen(true);
      console.log(err);
    });
    refreshBaseDemandLevels();
  }
  function changeHandler(index, key, value) {
    setBaseDemandLevels((prevState) => {
      return prevState.map((row, idx) =>
        idx === index
          ? {
              ...row,
              [key]: value,
            }
          : row
      );
    });
  }

  return {
    baseDemandLevels,
    bdlIsInEditMode: isInEditMode,
    setBdlIsInEditMode: setIsInEditMode,
    bdlChangeHandler: changeHandler,
    open,
    handleCloseStatus,
    networkMsg,
    setFromSetModel,
    state,
    addRow,
    patchRowValue,
  };
}
