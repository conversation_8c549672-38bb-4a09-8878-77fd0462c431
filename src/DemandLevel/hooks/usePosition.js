import { useEffect, useRef, useState } from "react";
export function usePosition() {
  const dlRef = useRef(null);
  const dowRef = useRef(null);
  const ltRef = useRef(null);
  const bdlRef = useRef(null);
  const [marginTop, setMarginTop] = useState(0);
  useEffect(() => {
    const [
      { height: dlHeight },
      { height: dowHeight },
      { height: ltHeight },
      { height: bdlHeight },
    ] = [
      dlRef.current.getBoundingClientRect(),
      dowRef.current.getBoundingClientRect(),
      ltRef.current.getBoundingClientRect(),
      bdlRef.current.getBoundingClientRect(),
    ];
    const maxHeightColumnExcludingBDL = Math.max(dlHeight, dowHeight, ltHeight);
    const maxHeightColumnIncludingBDL = Math.max(
      maxHeightColumnExcludingBDL,
      bdlHeight
    );
    const minVal = Math.min(
      maxHeightColumnExcludingBDL,
      maxHeightColumnIncludingBDL
    );
    const maxVal = Math.max(
      maxHeightColumnExcludingBDL,
      maxHeightColumnIncludingBDL
    );
    setMarginTop(minVal - maxVal + 84);
  }, [dlRef, dowRef, ltRef, bdlRef]);
  return {
    dlRef,
    dowRef,
    ltRef,
    bdlRef,
    marginTop,
  };
}
