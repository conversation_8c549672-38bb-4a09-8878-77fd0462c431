import { styled } from "@mui/system";
import {
  Box,
  Button,
  IconButton,
  InputLabel,
  MenuItem,
  Stack,
  Typography,
} from "@mui/material";
import React, { useState } from "react";
import { Close, CloseRounded } from "@mui/icons-material";
import { PrimaryButton } from "../../sdk";
import { TextField, TextFieldSmall } from "../../EventCalender/Styles";

function getModalStyle() {
  const top = 8;
  const right = 4;

  return {
    top: `${top}%`,
    right: `${right}%`,
    transform: `translate(-${top}, -${right})`,
  };
}

export const Section = styled(Box)`
  display: flex;
  flex-direction: column;
  gap: 22px;
  align-items: center;
  padding: 8px 24px 8px 24px;
  height: 200px;
  align-items: center;
`;
export const ChooseBtn = styled(InputLabel)`
  font-family: Roboto;
  font-size: 12px;
  font-weight: bold;
  line-height: 1.25;
  text-align:center;
  color: #000000;
  &:hover {
    cursor: pointer;
  }
  height: 30px;
  width: 150px;
  border: 1px solid #130453;
  border-radius: 5px;
  color: #130453;
  padding: 4px 10px;
`;
export const Filename = styled(Typography)`
  font: normal normal normal 12px/15px Roboto;
  margin-left: 10px;
`;
export const Title = styled(Typography)`
  font: normal normal normal 20px/25px Roboto;
  letter-spacing: 0px;
  color: #333333;
`;
export const Cross = styled(CloseRounded)`
  width: 16px;
  height: 16px;
  :hover {
    cursor: pointer;
  }
`;
export const Head = styled(Box)`
  top: 13px;
  left: 80px;
  color: white;
  z-index: 999;
`;
export const CrossSmall = styled(CloseRounded)`
  width: 12px;
  height: 12px;
  margin-top: -30px;
  margin-left: 10px;
  :hover {
    cursor: pointer;
  }
`;
export const UploadSection = styled(Box)`
  display: flex;
  width: 180px;
  justify-content: center;
  align-items: center;
`;

export const Done = styled(PrimaryButton)`
  width: 150px;
  height: 24px;
  background: #130453 0% 0% no-repeat padding-box;
  border: 1px solid #130453;
  border-radius: 5px;
  font: normal normal bold 12px/15px Roboto;
  letter-spacing: 0px;
  color: #ffffff;
`;

const StyledSection = styled(Section)(({ theme }) => ({
  position: "absolute",
  width: 350,
  height: "fit-content",
  backgroundColor: "#fff",
  boxShadow: 5,
}));

const uploadType = [
  "Combined Past data",
  "Revenue Past data",
  "Rooms Sold Past data",
];

export function ModelBody({
  fileHandler,
  fileGetter,
  setOpenSelectBar,
  removeFile,
  filename,
  fileType,
  setFileType,
  isLoading,
}) {
  const [modalStyle] = useState(getModalStyle);
  return (
    <StyledSection style={modalStyle}>
      <Head>
        <Title>Upload Past Data</Title>
        <IconButton onClick={() => setOpenSelectBar(false)}
          sx={{
            position:'absolute',
            top:-2,right:5
          }}
          >
          <Close />
        </IconButton>
      </Head>
      <TextFieldSmall
        id="UploadType"
        label="File Type"
        sx={{
          ".MuiInputBase-root": { height: "40px", width: "180px" },
        }}
        select
        value={fileType}
        onChange={(e) => setFileType(e.target.value)}
      >
        {uploadType.map((option, index) => (
          <MenuItem key={`${option}-${index}`} value={option}>
            {option}
          </MenuItem>
        ))}
      </TextFieldSmall>
      <Stack>
        <ChooseBtn
          style={filename ? { display: "none" } : { display: "block" }}
          htmlFor="upload"
        >
          Choose File
          <input
            type="file"
            id="upload"
            onChange={(e) => fileGetter(e.target.files)}
            style={{ display: "none" }}
          />
        </ChooseBtn>
      </Stack>
      {filename ? (
        <UploadSection style={{ display: "flex" }}>
          <img
            src="/assets/excel.svg"
            alt="Excel Icon"
            width="36px"
            height="36px"
          />
          {/* <File>{filename !== "" && <Filename>{filename}</Filename>}</File> */}
          <Filename>{filename}</Filename>
          <CrossSmall
            onClick={() => removeFile()}
            style={filename ? { display: "block" } : { display: "none" }}
          >
            &times;
          </CrossSmall>
        </UploadSection>
      ) : null}
      <Stack flexDirection="row" justifyContent="space-evenly" width="100%">
        <Done
          disabled={!filename}
          next
          onClick={fileHandler}
          type="button"
          loading={isLoading}
        >
          Upload
        </Done>
      </Stack>
    </StyledSection>
  );
}
