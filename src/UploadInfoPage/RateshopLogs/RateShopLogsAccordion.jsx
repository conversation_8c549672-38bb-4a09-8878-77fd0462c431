import React, { useCallback, useEffect, useState } from "react";
import {
  Box,
  CircularProgress,
  IconButton,
  Snackbar,
  SnackbarContent,
  styled,
  Table,
  TableBody,
  TableCell,
  tableCellClasses,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";
import { addDays, format } from "date-fns";
import { SaveAlt } from "@mui/icons-material";
import { useAuth } from "../../sdk";
import { RateShopStyledTableCell, RateShopStyledTableRow } from ".";
import { RateShopTableRows } from "./RateShopTableRows";
import { useParams } from "react-router-dom/cjs/react-router-dom.min";

const searchTime = (createdAt, successAt) => {
  const startTime = format(new Date(createdAt), "dd-MM-yyyy(HH:mm)");
  const successTime = successAt
    ? format(new Date(successAt), "dd-MM-yyyy (HH:mm)")
    : "In progress";
  return `${startTime}-${successTime}`;
};

const status = {
  SUBMIT: "In Progress",
  CREATED: "Completed",
  FAILED: "Failed",
};

const getRatelogDuration = (RateShopstatus, ratelogDuration) => {
  switch (RateShopstatus) {
    case "SUBMIT":
      return "In Progress";
    case "FAILED":
      return "0 minutes";
    default:
      return `${ratelogDuration || 0} minutes`;
  }
};
const getDateRange = (createdAt, requestString) => {
  let horizonexpression = "";
  try {
    horizonexpression = JSON.parse(requestString).horizonexpression || "";
  } catch (e) {
    console.error("Malformed requestString", e);
    return "--";
  }
  const [startRange, endRange] = horizonexpression.split("-");
  const initialDate = addDays(new Date(createdAt), startRange ?? 0);
  const finalDate = addDays(new Date(createdAt), endRange ?? startRange ?? 0);
  const startDate = format(initialDate, "dd-MM-yyyy");
  const endDate = format(finalDate, "dd-MM-yyyy");
  return `${startDate}-${endDate}`;
};
export const RateShopLogsAccordion = ({ item, rateShopRequestId }) => {
  const [isDownloadLoading, setDownloadLoading] = useState(null);
  const [networkMsg, setnetworkMsg] = useState(null);
  const [data, setData] = useState([]);

  const { authFetch } = useAuth();
  const { hotelId } = useParams();
  const [isLoading, setIsLoading] = useState(false);

  const hanldeDownloadExcelFile = useCallback(
    async (queueId, index) => {
      setDownloadLoading(queueId);
      const { get } = await authFetch({
        path: `/queueIdToExcel-parse?queueid=${queueId}`,
      });

      const response = await get();
      const blob = await response.response.blob();
      const url = window.URL.createObjectURL(new Blob([blob]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", `${queueId}.xlsx`);
      document.body.appendChild(link);
      await link.click();
      setDownloadLoading(null);

      setnetworkMsg("File Downloaded Successfully");
    },
    [authFetch]
  );
  const getAccordionDetails = useCallback(async () => {
    try {
      setIsLoading(true);
      const { get } = await authFetch({
        path: `/hotel/${hotelId}/requests/${rateShopRequestId}`,
      });
      const { data, error } = await get();
      if (data) {
        setData(data ?? []);
      } else {
        console.log(error);
      }
    } catch (error) {
      console.error("Failed to fetch accordion details:", error);
    } finally {
      setIsLoading(false);
    }
  }, [authFetch, hotelId, rateShopRequestId]);
  const columns = [
    {
      id: "queue_id",
      label: "Queue ID",
      align: "center",
      renderValue: (item) => item?.queue_id,
    },
    {
      id: "duration",
      label: "Duration",
      align: "center",
      renderValue: (item) =>
        getRatelogDuration(item?.status, item?.rateShopDurationTime),
    },
    {
      id: "search_time",
      label: "Search Time",
      align: "center",
      renderValue: (item) =>
        searchTime(
          item?.createdAt,
          item?.status === "FAILED" ? item?.createdAt : item?.successAt
        ),
    },
    {
      id: "date_range",
      label: "Date Range",
      align: "center",
      renderValue: (item) => getDateRange(item?.createdAt, item?.requestString),
      style: {
        minWidth: "200px",
      },
    },
    {
      id: "status",
      label: "Status",
      align: "center",
      renderValue: (item) => status?.[item?.status] ?? "",
    },

    {
      id: "download",
      label: "",
      align: "center",
      renderValue: (item, index) =>
        "CREATED" === item?.status && (
          <IconButton
            disabled={isDownloadLoading === item?.queue_id}
            onClick={() => hanldeDownloadExcelFile(item.queue_id, index)}
          >
            {isDownloadLoading === item?.queue_id ? (
              <CircularProgress color="inherit" />
            ) : (
              <SaveAlt />
            )}
          </IconButton>
        ),
    },
  ];

  useEffect(() => {
    getAccordionDetails();
  }, [getAccordionDetails]);

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" p={2}>
        <CircularProgress />
      </Box>
    );
  }
  return (
    <Box sx={{ margin: 1, padding: "0 60px" }}>
      <Table aria-label="purchases">
        <TableHead>
          <RateShopStyledTableRow
            sx={{
              position: "sticky",
              top: 0,
              backgroundColor: "white",
              zIndex: 1,
            }}
          >
            {columns.map((column) => (
              <StyledTableCell
                key={`${column?.id}-accordion`}
                style={column?.style}
              >
                {column.label}
              </StyledTableCell>
            ))}
          </RateShopStyledTableRow>
        </TableHead>
        <TableBody>
          {!data?.length && (
            <TableRow>
              <RateShopStyledTableCell colSpan={13}>
                <Typography align="center">No data found</Typography>
              </RateShopStyledTableCell>
            </TableRow>
          )}

          {data?.map((item, index) => (
            <TableRow key={item?.queue_id ?? index} sx={{ height: "50px" }}>
              <RateShopTableRows index={index} item={item} columns={columns} />
            </TableRow>
          ))}
        </TableBody>
      </Table>
      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={networkMsg}
        autoHideDuration={3000}
        onClose={() => setnetworkMsg(null)}
      >
        {networkMsg && <SnackbarContent message={networkMsg} />}
      </Snackbar>
    </Box>
  );
};

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    color: "#ffffff",
    background: "#306fbc",
    textAlign: "center",
  },
  [`&.${tableCellClasses.body}`]: {
    padding: " 6px 10px",
    textAlign: "center",
  },
}));
