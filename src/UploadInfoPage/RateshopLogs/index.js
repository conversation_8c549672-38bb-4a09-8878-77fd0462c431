import React, {
  Fragment,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";
import {
  Autocomplete,
  Paper,
  Stack,
  Snackbar,
  SnackbarContent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  styled,
  tableCellClasses,
  Collapse,
  IconButton,
} from "@mui/material";
import { addDays, format } from "date-fns";
import { useParams } from "react-router-dom";
import { LoadingPage, useAuth } from "../../sdk";
import { RateShopTableRows } from "./RateShopTableRows";
import { RateShopLogsAccordion } from "./RateShopLogsAccordion";
import { ExpandLess, ExpandMore } from "@mui/icons-material";

const status = {
  SUBMIT: "In Progress",
  CREATED: "Completed",
  FAILED: "Failed",
};

// Helper functions moved outside component

const losAndGuests = (requestString, cell) => {
  const requestObject = JSON.parse(requestString);
  if (cell === "los") {
    return requestObject?.los;
  } else {
    return requestObject?.occupancy;
  }
};
const parseRequestString = (requestString) => {
  try {
    return JSON.parse(requestString);
  } catch (error) {
    console.error("Failed to parse request string:", error);
    return {};
  }
};
const getRatelogDuration = (RateShopstatus, ratelogDuration) => {
  switch (RateShopstatus) {
    case "SUBMIT":
      return "In Progress";
    case "FAILED":
      return "0 minutes";
    default:
      return `${Math.round(ratelogDuration) || 0} minutes`;
  }
};

const searchTime = (createdAt, successAt) => {
  const startTime = format(new Date(createdAt), "dd-MM-yyyy(HH:mm)");
  const successTime = successAt
    ? format(new Date(successAt), "dd-MM-yyyy (HH:mm)")
    : "In progress";
  return `${startTime}-${successTime}`;
};

const getDateRange = (createdAt, requestString) => {
  const horizonexpression = JSON.parse(requestString).horizonexpression;
  const [startRange, endRange] = horizonexpression.split("-");
  const initialDate = addDays(new Date(createdAt), startRange ?? 0);
  const finalDate = addDays(new Date(createdAt), endRange ?? startRange ?? 0);
  const startDate = format(initialDate, "dd-MM-yyyy");
  const endDate = format(finalDate, "dd-MM-yyyy");
  return `${startDate}-${endDate}`;
};

const hasCompetitors = (requestString) => {
  const requestObject = parseRequestString(requestString);
  return (requestObject.hotelcodes?.length || 0) > 1 ? "Yes" : "No";
};

const filterOptions = {
  requestType: [
    {
      label: "Scheduled",
      value: "scheduled",
    },
    {
      label: "On Demand",
      value: "on_demand",
    },
  ],
  duration: [
    {
      label: "Less than 15 minutes",
      value: "15",
    },
    {
      label: "Less than 30 minutes",
      value: "30",
    },
    {
      label: "Less than 45 minutes",
      value: "45",
    },
  ],
};

export default function RateshopLogs() {
  const { hotelId } = useParams();
  const { authFetch } = useAuth();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [isLoading, setIsLoading] = useState(false);
  const [sourcesData, setSourcesData] = useState([]);
  const [filter, setFilter] = useState({});
  const [data, setData] = useState([]);
  const [count, setCount] = useState(0);
  const [networkMsg, setnetworkMsg] = useState(null);
  const [openAccordion, setOpenAccordion] = useState([]); // will store rateShopRequestId for all open accordions

  const getSources = useCallback(async () => {
    setIsLoading(true);
    const { get } = await authFetch({
      path: `/sources`,
    });
    const { data, error } = await get();
    if (data) {
      setSourcesData(data);
    } else {
      console.log(error);
    }
    setIsLoading(false);
  }, [authFetch]);

  const handleOpenAccordion = (e, rateShopRequestId) => {
    e.stopPropagation();
    console.log("onClick", rateShopRequestId);

    setOpenAccordion((prev) => {
      if (prev?.includes(rateShopRequestId)) {
        return [prev.filter((item) => item === rateShopRequestId)];
      } else {
        return [...prev, rateShopRequestId];
      }
    });
  };
  const sourceName = useCallback(
    (sources) => {
      const names = sources.map((sourceId) => {
        const source = sourcesData?.find((item) => item?.sourceId === sourceId);
        return source?.name;
      });
      return names.map((item) => <Typography>{item}</Typography>);
    },
    [sourcesData]
  );

  const columns = useMemo(
    () => [
      {
        id: "action",
        label: "",
        align: "center",
        renderValue: (item) =>
          openAccordion?.includes(item?.rateShopRequestId) ? (
            <IconButton
              onClick={(e) => handleOpenAccordion(e, item?.rateShopRequestId)}
            >
              <ExpandLess />
            </IconButton>
          ) : (
            <IconButton
              onClick={(e) => handleOpenAccordion(e, item?.rateShopRequestId)}
            >
              <ExpandMore />
            </IconButton>
          ),
      },
      {
        id: "rateShopRequestId",
        label: "Request ID",
        align: "center",
        renderValue: (item) => item?.rateShopRequestId,
      },
      {
        id: "search_time",
        label: "Search Time",
        align: "center",
        renderValue: (item) =>
          searchTime(
            item?.createdAt,
            item?.status === "FAILED" ? item?.createdAt : item?.successAt
          ),
      },
      {
        id: "duration",
        label: "Duration",
        align: "center",
        renderValue: (item) =>
          getRatelogDuration(item?.status, item?.rateShopDurationTime),
      },
      {
        id: "ota",
        label: "OTA",
        align: "center",
        renderValue: (item) => sourceName(item?.Sources),
      },
      {
        id: "platform",
        label: "Platform",
        align: "center",
        renderValue: (item) => (item.isMobile ? "Mobile" : "Desktop"),
      },
      {
        id: "los",
        label: "LOS",
        align: "center",
        renderValue: (item) => losAndGuests(item?.requestString, "los"),
      },
      {
        id: "guest",
        label: "Guest",
        align: "center",
        renderValue: (item) => losAndGuests(item?.requestString, "guests"),
      },
      {
        id: "requested_by",
        label: "Requested By",
        align: "center",
        renderValue: (item) =>
          item?.createdBy && item?.email
            ? `${item?.createdBy}\n(${item?.email})`
            : "--",
      },
      {
        id: "date_range",
        label: "Date Range",
        align: "center",
        renderValue: (item) =>
          getDateRange(item?.createdAt, item?.requestString),
        style: {
          minWidth: "200px",
        },
      },
      {
        id: "competitors",
        label: "Competitors",
        align: "center",
        renderValue: (item) => hasCompetitors(item?.requestString),
      },
      {
        id: "rateShop_type",
        label: "RateShop Type",
        align: "center",
        renderValue: (item) => (item?.isScheduled ? "Scheduled" : "On demand"),
      },
      {
        id: "status",
        label: "Status",
        align: "center",
        renderValue: (item) => status?.[item?.status] ?? "",
      },
    ],
    [openAccordion, sourceName]
  );

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(+event.target.value);
    setPage(0);
  };

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const getRateShopLogs = useCallback(async () => {
    setIsLoading(true);
    const { get } = await authFetch({
      path: `/hotel/${hotelId}/rate-shop-hotel-logs?page=${page}&limit=${rowsPerPage}&searchTime=${
        filter.duration?.value ?? ""
      }&rateShopType=${filter.requestType?.value ?? ""}`,
    });
    const { data, error } = await get();
    if (data) {
      setData(data?.rateShopLogDetails ?? []);
      setCount(data?.totalCount);
    } else {
      console.log(error);
    }
    setIsLoading(false);
  }, [
    authFetch,
    filter.duration,
    filter.requestType,
    hotelId,
    page,
    rowsPerPage,
  ]);

  useEffect(() => {
    getSources();
  }, [getSources]);

  useEffect(() => {
    getRateShopLogs();
  }, [getRateShopLogs]);

  return isLoading ? (
    <LoadingPage />
  ) : (
    <>
      <Stack
        direction={"row"}
        gap={2}
        paddingBottom={2}
        justifyContent={"flex-end"}
      >
        <Autocomplete
          value={filter.requestType}
          style={{ width: 200, padding: "0 8px 0 8px" }}
          options={filterOptions.requestType}
          size="small"
          getOptionLabel={(option) => option.label}
          classes={{
            paper: "dropdownStyle",
          }}
          onChange={(event, newValue) => {
            setFilter((prev) => ({ ...prev, requestType: newValue }));
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              sx={{ borderRadius: 500 }}
              label="Request Type"
            />
          )}
        />
        <Autocomplete
          value={filter.duration}
          style={{ width: 230, padding: "0 8px 0 8px" }}
          options={filterOptions.duration}
          size="small"
          getOptionLabel={(option) => option.label}
          classes={{
            paper: "dropdownStyle",
          }}
          onChange={(event, newValue) => {
            setFilter((prev) => ({ ...prev, duration: newValue }));
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              sx={{ borderRadius: 500 }}
              label="Duration"
            />
          )}
        />
      </Stack>
      <TableContainer
        component={Paper}
        sx={{
          maxHeight: "65vh",
          overflowY: "auto",
        }}
      >
        <Table>
          <TableHead>
            <RateShopStyledTableRow
              sx={{
                position: "sticky",
                top: 0,
                backgroundColor: "white",
                zIndex: 1,
              }}
            >
              {columns.map((column) => (
                <RateShopStyledTableCell key={column?.id} style={column?.style}>
                  {column.label}
                </RateShopStyledTableCell>
              ))}
            </RateShopStyledTableRow>
          </TableHead>

          <TableBody>
            {!data?.length && (
              <RateShopStyledTableRow>
                <RateShopStyledTableCell colSpan={13}>
                  <Typography align="center">No data found</Typography>
                </RateShopStyledTableCell>
              </RateShopStyledTableRow>
            )}

            {data.map((item, index) => (
              <Fragment key={item?.rateShopRequestId ?? index}>
                <RateShopStyledTableRow
                  onClick={(e) =>
                    handleOpenAccordion(e, item?.rateShopRequestId)
                  }
                >
                  <RateShopTableRows
                    index={index}
                    item={item}
                    columns={columns}
                  />
                </RateShopStyledTableRow>

                <RateShopStyledTableRow>
                  <RateShopStyledTableCell
                    colSpan={columns.length}
                    sx={{ padding: 0, border: 0 }}
                  >
                    <Collapse
                      in={openAccordion?.includes(item?.rateShopRequestId)}
                      timeout="auto"
                      unmountOnExit
                    >
                      <RateShopLogsAccordion
                        rateShopRequestId={item?.rateShopRequestId}
                        item={item}
                      />
                    </Collapse>
                  </RateShopStyledTableCell>
                </RateShopStyledTableRow>
              </Fragment>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        rowsPerPageOptions={[10, 25, 50]}
        component="div"
        count={count}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelDisplayedRows={({ from, to }) => null}
        nextIconButtonProps={{
          disabled: data.length < rowsPerPage,
        }}
      />
      <Snackbar
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        open={networkMsg}
        autoHideDuration={3000}
        onClose={() => setnetworkMsg(null)}
      >
        {networkMsg && <SnackbarContent message={networkMsg} />}
      </Snackbar>
    </>
  );
}

export const RateShopStyledTableCell = styled(TableCell)(({ theme }) => ({
  [`&.${tableCellClasses.head}`]: {
    backgroundColor: theme.palette.common.white,
    color: "#130453",
    textAlign: "center",
    font: "normal normal bold 16px/20px Roboto",
    padding: "16px",
    letterSpacing: "0px",
    width: "11%",
  },
  [`&.${tableCellClasses.body}`]: {
    fontSize: 14,
    textAlign: "center",
    width: "11%",
  },
}));

export const RateShopStyledTableRow = styled(TableRow)(({ theme }) => ({
  "&:nth-of-type(odd)": {
    backgroundColor: theme.palette.action.hover,
  },
  // hide last border
  "&:last-child td, &:last-child th": {
    border: 0,
  },
}));
